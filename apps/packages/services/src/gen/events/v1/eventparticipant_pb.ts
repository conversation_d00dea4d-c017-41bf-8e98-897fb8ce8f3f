// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file events/v1/eventparticipant.proto (package api.events.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type {
  PaginationDetails,
  PaginationRequest,
} from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import type { Profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";
import type { Event } from "./events_pb";
import { file_events_v1_events } from "./events_pb";

/**
 * Describes the file events/v1/eventparticipant.proto.
 */
export const file_events_v1_eventparticipant: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_authz_v1_authz,
      file_events_v1_events,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.events.v1.EventParticipation
 */
export type EventParticipation = Message<"api.events.v1.EventParticipation"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: int64 event_id = 2;
   */
  eventId: bigint;

  /**
   * @generated from field: int64 vtuber_id = 3;
   */
  vtuberId: bigint;

  /**
   * @gotag: validate:"required,oneof=approved rejected"
   *
   * @generated from field: string status = 4;
   */
  status: string;

  /**
   * @generated from field: optional api.shared.v1.Profile vtuber = 5;
   */
  vtuber?: Profile;

  /**
   * @generated from field: optional api.events.v1.Event event = 6;
   */
  event?: Event;

  /**
   * @generated from field: int64 vote_count = 7;
   */
  voteCount: bigint;

  /**
   * @generated from field: optional string remarks = 8;
   */
  remarks?: string;
};

/**
 * Describes the message api.events.v1.EventParticipation.
 * Use `create(EventParticipationSchema)` to create a new message.
 */
export const EventParticipationSchema: GenMessage<EventParticipation> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 0);

/**
 * @generated from message api.events.v1.AddEventParticipationRequest
 */
export type AddEventParticipationRequest =
  Message<"api.events.v1.AddEventParticipationRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 event_id = 1;
     */
    eventId: bigint;
  };

/**
 * Describes the message api.events.v1.AddEventParticipationRequest.
 * Use `create(AddEventParticipationRequestSchema)` to create a new message.
 */
export const AddEventParticipationRequestSchema: GenMessage<AddEventParticipationRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 1);

/**
 * @generated from message api.events.v1.GetCreatorEventParticipationRequest
 */
export type GetCreatorEventParticipationRequest =
  Message<"api.events.v1.GetCreatorEventParticipationRequest"> & {};

/**
 * Describes the message api.events.v1.GetCreatorEventParticipationRequest.
 * Use `create(GetCreatorEventParticipationRequestSchema)` to create a new message.
 */
export const GetCreatorEventParticipationRequestSchema: GenMessage<GetCreatorEventParticipationRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 2);

/**
 * @generated from message api.events.v1.GetCreatorEventParticipationResponse
 */
export type GetCreatorEventParticipationResponse =
  Message<"api.events.v1.GetCreatorEventParticipationResponse"> & {
    /**
     * @generated from field: repeated int64 events = 1;
     */
    events: bigint[];
  };

/**
 * Describes the message api.events.v1.GetCreatorEventParticipationResponse.
 * Use `create(GetCreatorEventParticipationResponseSchema)` to create a new message.
 */
export const GetCreatorEventParticipationResponseSchema: GenMessage<GetCreatorEventParticipationResponse> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 3);

/**
 * @generated from message api.events.v1.GetEventParticipationOfVtuberRequest
 */
export type GetEventParticipationOfVtuberRequest =
  Message<"api.events.v1.GetEventParticipationOfVtuberRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 vtuber_id = 1;
     */
    vtuberId: bigint;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.events.v1.GetEventParticipationOfVtuberRequest.
 * Use `create(GetEventParticipationOfVtuberRequestSchema)` to create a new message.
 */
export const GetEventParticipationOfVtuberRequestSchema: GenMessage<GetEventParticipationOfVtuberRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 4);

/**
 * @generated from message api.events.v1.GetEventParticipantsByEventIdRequest
 */
export type GetEventParticipantsByEventIdRequest =
  Message<"api.events.v1.GetEventParticipantsByEventIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string event_id = 1;
     */
    eventId: string;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.events.v1.GetEventParticipantsByEventIdRequest.
 * Use `create(GetEventParticipantsByEventIdRequestSchema)` to create a new message.
 */
export const GetEventParticipantsByEventIdRequestSchema: GenMessage<GetEventParticipantsByEventIdRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 5);

/**
 * @generated from message api.events.v1.GetEventParticipantsByEventIdResponse
 */
export type GetEventParticipantsByEventIdResponse =
  Message<"api.events.v1.GetEventParticipantsByEventIdResponse"> & {
    /**
     * @generated from field: repeated api.events.v1.EventParticipation data = 1;
     */
    data: EventParticipation[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.events.v1.GetEventParticipantsByEventIdResponse.
 * Use `create(GetEventParticipantsByEventIdResponseSchema)` to create a new message.
 */
export const GetEventParticipantsByEventIdResponseSchema: GenMessage<GetEventParticipantsByEventIdResponse> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 6);

/**
 * @generated from message api.events.v1.GetMyEventParticipationRequest
 */
export type GetMyEventParticipationRequest =
  Message<"api.events.v1.GetMyEventParticipationRequest"> & {
    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.events.v1.GetMyEventParticipationRequest.
 * Use `create(GetMyEventParticipationRequestSchema)` to create a new message.
 */
export const GetMyEventParticipationRequestSchema: GenMessage<GetMyEventParticipationRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 7);

/**
 * @generated from message api.events.v1.GetMyEventParticipationResponse
 */
export type GetMyEventParticipationResponse =
  Message<"api.events.v1.GetMyEventParticipationResponse"> & {
    /**
     * @generated from field: repeated api.events.v1.EventParticipation data = 1;
     */
    data: EventParticipation[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.events.v1.GetMyEventParticipationResponse.
 * Use `create(GetMyEventParticipationResponseSchema)` to create a new message.
 */
export const GetMyEventParticipationResponseSchema: GenMessage<GetMyEventParticipationResponse> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 8);

/**
 * @generated from message api.events.v1.ChangeStatusRequest
 */
export type ChangeStatusRequest =
  Message<"api.events.v1.ChangeStatusRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 event_participation_id = 1;
     */
    eventParticipationId: bigint;

    /**
     * @gotag: validate:"required,oneof=approved rejected"
     *
     * @generated from field: string status = 2;
     */
    status: string;

    /**
     * @generated from field: optional string reason = 3;
     */
    reason?: string;
  };

/**
 * Describes the message api.events.v1.ChangeStatusRequest.
 * Use `create(ChangeStatusRequestSchema)` to create a new message.
 */
export const ChangeStatusRequestSchema: GenMessage<ChangeStatusRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 9);

/**
 * @generated from message api.events.v1.GetAllEventParticipationRequest
 */
export type GetAllEventParticipationRequest =
  Message<"api.events.v1.GetAllEventParticipationRequest"> & {
    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.events.v1.GetAllEventParticipationRequest.
 * Use `create(GetAllEventParticipationRequestSchema)` to create a new message.
 */
export const GetAllEventParticipationRequestSchema: GenMessage<GetAllEventParticipationRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 10);

/**
 * @generated from message api.events.v1.GetAllEventParticipationResponse
 */
export type GetAllEventParticipationResponse =
  Message<"api.events.v1.GetAllEventParticipationResponse"> & {
    /**
     * @generated from field: repeated api.events.v1.EventParticipation data = 1;
     */
    data: EventParticipation[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.events.v1.GetAllEventParticipationResponse.
 * Use `create(GetAllEventParticipationResponseSchema)` to create a new message.
 */
export const GetAllEventParticipationResponseSchema: GenMessage<GetAllEventParticipationResponse> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 11);

/**
 * @generated from message api.events.v1.GetTopTenEventParticipantsVtuberRequest
 */
export type GetTopTenEventParticipantsVtuberRequest =
  Message<"api.events.v1.GetTopTenEventParticipantsVtuberRequest"> & {
    /**
     * @generated from field: int64 event_id = 1;
     */
    eventId: bigint;
  };

/**
 * Describes the message api.events.v1.GetTopTenEventParticipantsVtuberRequest.
 * Use `create(GetTopTenEventParticipantsVtuberRequestSchema)` to create a new message.
 */
export const GetTopTenEventParticipantsVtuberRequestSchema: GenMessage<GetTopTenEventParticipantsVtuberRequest> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 12);

/**
 * @generated from message api.events.v1.GetTopTenEventParticipantsVtuberResponse
 */
export type GetTopTenEventParticipantsVtuberResponse =
  Message<"api.events.v1.GetTopTenEventParticipantsVtuberResponse"> & {
    /**
     * @generated from field: repeated api.events.v1.EventParticipantVtuber data = 1;
     */
    data: EventParticipantVtuber[];
  };

/**
 * Describes the message api.events.v1.GetTopTenEventParticipantsVtuberResponse.
 * Use `create(GetTopTenEventParticipantsVtuberResponseSchema)` to create a new message.
 */
export const GetTopTenEventParticipantsVtuberResponseSchema: GenMessage<GetTopTenEventParticipantsVtuberResponse> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 13);

/**
 * @generated from message api.events.v1.EventParticipantVtuber
 */
export type EventParticipantVtuber =
  Message<"api.events.v1.EventParticipantVtuber"> & {
    /**
     * @generated from field: string name = 1;
     */
    name: string;

    /**
     * @generated from field: optional string image = 2;
     */
    image?: string;

    /**
     * @generated from field: int32 vote_count = 3;
     */
    voteCount: number;

    /**
     * @generated from field: int64 id = 4;
     */
    id: bigint;
  };

/**
 * Describes the message api.events.v1.EventParticipantVtuber.
 * Use `create(EventParticipantVtuberSchema)` to create a new message.
 */
export const EventParticipantVtuberSchema: GenMessage<EventParticipantVtuber> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 14);

/**
 * @generated from message api.events.v1.AddEventParticipationResponse
 */
export type AddEventParticipationResponse =
  Message<"api.events.v1.AddEventParticipationResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.events.v1.AddEventParticipationResponse.
 * Use `create(AddEventParticipationResponseSchema)` to create a new message.
 */
export const AddEventParticipationResponseSchema: GenMessage<AddEventParticipationResponse> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 15);

/**
 * @generated from message api.events.v1.ChangeStatusResponse
 */
export type ChangeStatusResponse =
  Message<"api.events.v1.ChangeStatusResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.events.v1.ChangeStatusResponse.
 * Use `create(ChangeStatusResponseSchema)` to create a new message.
 */
export const ChangeStatusResponseSchema: GenMessage<ChangeStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_events_v1_eventparticipant, 16);

/**
 * @generated from service api.events.v1.EventParticipantService
 */
export const EventParticipantService: GenService<{
  /**
   * @generated from rpc api.events.v1.EventParticipantService.AddEventParticipation
   */
  addEventParticipation: {
    methodKind: "unary";
    input: typeof AddEventParticipationRequestSchema;
    output: typeof AddEventParticipationResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventParticipantService.GetEventParticipantsByEventId
   */
  getEventParticipantsByEventId: {
    methodKind: "unary";
    input: typeof GetEventParticipantsByEventIdRequestSchema;
    output: typeof GetEventParticipantsByEventIdResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventParticipantService.GetMyEventParticipation
   */
  getMyEventParticipation: {
    methodKind: "unary";
    input: typeof GetMyEventParticipationRequestSchema;
    output: typeof GetMyEventParticipationResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventParticipantService.ChangeStatus
   */
  changeStatus: {
    methodKind: "unary";
    input: typeof ChangeStatusRequestSchema;
    output: typeof ChangeStatusResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventParticipantService.GetAllEventParticipation
   */
  getAllEventParticipation: {
    methodKind: "unary";
    input: typeof GetAllEventParticipationRequestSchema;
    output: typeof GetAllEventParticipationResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventParticipantService.GetCreatorEventParticipation
   */
  getCreatorEventParticipation: {
    methodKind: "unary";
    input: typeof GetCreatorEventParticipationRequestSchema;
    output: typeof GetCreatorEventParticipationResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventParticipantService.GetAllEventParticipantsByEventId
   */
  getAllEventParticipantsByEventId: {
    methodKind: "unary";
    input: typeof GetEventParticipantsByEventIdRequestSchema;
    output: typeof GetEventParticipantsByEventIdResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventParticipantService.GetAllEventParticipationOfVtuber
   */
  getAllEventParticipationOfVtuber: {
    methodKind: "unary";
    input: typeof GetEventParticipationOfVtuberRequestSchema;
    output: typeof GetAllEventParticipationResponseSchema;
  };
  /**
   * @generated from rpc api.events.v1.EventParticipantService.GetTopTenEventParticipantsVtubers
   */
  getTopTenEventParticipantsVtubers: {
    methodKind: "unary";
    input: typeof GetTopTenEventParticipantsVtuberRequestSchema;
    output: typeof GetTopTenEventParticipantsVtuberResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_events_v1_eventparticipant, 0);
