// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file vtubers/v1/vtubercategory.proto (package api.vtubers.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";

/**
 * Describes the file vtubers/v1/vtubercategory.proto.
 */
export const file_vtubers_v1_vtubercategory: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Ch92dHViZXJzL3YxL3Z0dWJlcmNhdGVnb3J5LnByb3RvEg5hcGkudnR1YmVycy52MSIcCg5WdHViZXJDYXRlZ29yeRIKCgJpZBgBIAEoAyIfCh1HZXRBbGxWdHViZXJDYXRlZ29yaWVzUmVxdWVzdCJLChtHZXRWdHViZXJDYXRlZ29yaWVzUmVzcG9uc2USLAoEZGF0YRgBIAMoCzIeLmFwaS52dHViZXJzLnYxLlZ0dWJlckNhdGVnb3J5Mo0BChVWdHViZXJDYXRlZ29yeVNlcnZpY2USdAoWR2V0QWxsVnR1YmVyQ2F0ZWdvcmllcxItLmFwaS52dHViZXJzLnYxLkdldEFsbFZ0dWJlckNhdGVnb3JpZXNSZXF1ZXN0GisuYXBpLnZ0dWJlcnMudjEuR2V0VnR1YmVyQ2F0ZWdvcmllc1Jlc3BvbnNlQjRaMmdpdGh1Yi5jb20vbnNwLWluYy92dHViZXIvYXBpL3Z0dWJlcnMvdjE7dnR1YmVyc3YxYgZwcm90bzM",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.vtubers.v1.VtuberCategory
 */
export type VtuberCategory = Message<"api.vtubers.v1.VtuberCategory"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.vtubers.v1.VtuberCategory.
 * Use `create(VtuberCategorySchema)` to create a new message.
 */
export const VtuberCategorySchema: GenMessage<VtuberCategory> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtubercategory, 0);

/**
 * @generated from message api.vtubers.v1.GetAllVtuberCategoriesRequest
 */
export type GetAllVtuberCategoriesRequest =
  Message<"api.vtubers.v1.GetAllVtuberCategoriesRequest"> & {};

/**
 * Describes the message api.vtubers.v1.GetAllVtuberCategoriesRequest.
 * Use `create(GetAllVtuberCategoriesRequestSchema)` to create a new message.
 */
export const GetAllVtuberCategoriesRequestSchema: GenMessage<GetAllVtuberCategoriesRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtubercategory, 1);

/**
 * @generated from message api.vtubers.v1.GetVtuberCategoriesResponse
 */
export type GetVtuberCategoriesResponse =
  Message<"api.vtubers.v1.GetVtuberCategoriesResponse"> & {
    /**
     * @generated from field: repeated api.vtubers.v1.VtuberCategory data = 1;
     */
    data: VtuberCategory[];
  };

/**
 * Describes the message api.vtubers.v1.GetVtuberCategoriesResponse.
 * Use `create(GetVtuberCategoriesResponseSchema)` to create a new message.
 */
export const GetVtuberCategoriesResponseSchema: GenMessage<GetVtuberCategoriesResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtubercategory, 2);

/**
 * @generated from service api.vtubers.v1.VtuberCategoryService
 */
export const VtuberCategoryService: GenService<{
  /**
   * @generated from rpc api.vtubers.v1.VtuberCategoryService.GetAllVtuberCategories
   */
  getAllVtuberCategories: {
    methodKind: "unary";
    input: typeof GetAllVtuberCategoriesRequestSchema;
    output: typeof GetVtuberCategoriesResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_vtubers_v1_vtubercategory, 0);
