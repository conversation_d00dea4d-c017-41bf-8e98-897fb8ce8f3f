// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file billing/v1/transactions.proto (package billing.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type {
  PaginationDetails,
  PaginationRequest,
} from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";

/**
 * Describes the file billing/v1/transactions.proto.
 */
export const file_billing_v1_transactions: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
    ],
  );

/**
 * @generated from message billing.v1.AddTransactionRequest
 */
export type AddTransactionRequest =
  Message<"billing.v1.AddTransactionRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int32 amount = 1;
     */
    amount: number;

    /**
     * @generated from field: optional int64 camp_variant_id = 2;
     */
    campVariantId?: bigint;

    /**
     * @generated from field: optional int64 creator_subscription_id = 3;
     */
    creatorSubscriptionId?: bigint;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string status = 4;
     */
    status: string;

    /**
     * @generated from field: bool is_recurring = 5;
     */
    isRecurring: boolean;

    /**
     * @generated from field: google.protobuf.Timestamp expires_on = 8;
     */
    expiresOn?: Timestamp;
  };

/**
 * Describes the message billing.v1.AddTransactionRequest.
 * Use `create(AddTransactionRequestSchema)` to create a new message.
 */
export const AddTransactionRequestSchema: GenMessage<AddTransactionRequest> =
  /*@__PURE__*/
  messageDesc(file_billing_v1_transactions, 0);

/**
 * @generated from message billing.v1.AddTransactionResponse
 */
export type AddTransactionResponse =
  Message<"billing.v1.AddTransactionResponse"> & {
    /**
     * @generated from field: billing.v1.Transaction data = 2;
     */
    data?: Transaction;
  };

/**
 * Describes the message billing.v1.AddTransactionResponse.
 * Use `create(AddTransactionResponseSchema)` to create a new message.
 */
export const AddTransactionResponseSchema: GenMessage<AddTransactionResponse> =
  /*@__PURE__*/
  messageDesc(file_billing_v1_transactions, 1);

/**
 * @generated from message billing.v1.Transaction
 */
export type Transaction = Message<"billing.v1.Transaction"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: int64 user_id = 2;
   */
  userId: bigint;

  /**
   * @generated from field: int32 amount = 3;
   */
  amount: number;

  /**
   * @generated from field: string type = 4;
   */
  type: string;

  /**
   * @generated from field: optional int64 camp_variant_id = 5;
   */
  campVariantId?: bigint;

  /**
   * @generated from field: optional int64 creator_subscription_id = 6;
   */
  creatorSubscriptionId?: bigint;

  /**
   * @generated from field: string status = 7;
   */
  status: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 8;
   */
  createdAt?: Timestamp;
};

/**
 * Describes the message billing.v1.Transaction.
 * Use `create(TransactionSchema)` to create a new message.
 */
export const TransactionSchema: GenMessage<Transaction> =
  /*@__PURE__*/
  messageDesc(file_billing_v1_transactions, 2);

/**
 * @generated from message billing.v1.GetAllTransactionsRequest
 */
export type GetAllTransactionsRequest =
  Message<"billing.v1.GetAllTransactionsRequest"> & {
    /**
     * @generated from field: optional int64 creator_subscription_id = 1;
     */
    creatorSubscriptionId?: bigint;

    /**
     * @generated from field: optional int64 user_id = 2;
     */
    userId?: bigint;

    /**
     * @generated from field: optional int64 camp_variant_id = 3;
     */
    campVariantId?: bigint;

    /**
     * @generated from field: api.shared.v1.PaginationRequest pagination = 4;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message billing.v1.GetAllTransactionsRequest.
 * Use `create(GetAllTransactionsRequestSchema)` to create a new message.
 */
export const GetAllTransactionsRequestSchema: GenMessage<GetAllTransactionsRequest> =
  /*@__PURE__*/
  messageDesc(file_billing_v1_transactions, 3);

/**
 * @generated from message billing.v1.GetAllTransactionsResponse
 */
export type GetAllTransactionsResponse =
  Message<"billing.v1.GetAllTransactionsResponse"> & {
    /**
     * @generated from field: repeated billing.v1.Transaction data = 1;
     */
    data: Transaction[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message billing.v1.GetAllTransactionsResponse.
 * Use `create(GetAllTransactionsResponseSchema)` to create a new message.
 */
export const GetAllTransactionsResponseSchema: GenMessage<GetAllTransactionsResponse> =
  /*@__PURE__*/
  messageDesc(file_billing_v1_transactions, 4);

/**
 * @generated from message billing.v1.GetAllTransactionsOfUserRequest
 */
export type GetAllTransactionsOfUserRequest =
  Message<"billing.v1.GetAllTransactionsOfUserRequest"> & {
    /**
     * @generated from field: optional int64 creator_subscription_id = 1;
     */
    creatorSubscriptionId?: bigint;

    /**
     * @generated from field: optional int64 camp_variant_id = 3;
     */
    campVariantId?: bigint;

    /**
     * @generated from field: api.shared.v1.PaginationRequest pagination = 4;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message billing.v1.GetAllTransactionsOfUserRequest.
 * Use `create(GetAllTransactionsOfUserRequestSchema)` to create a new message.
 */
export const GetAllTransactionsOfUserRequestSchema: GenMessage<GetAllTransactionsOfUserRequest> =
  /*@__PURE__*/
  messageDesc(file_billing_v1_transactions, 5);

/**
 * @generated from message billing.v1.GetTransactionByIdRequest
 */
export type GetTransactionByIdRequest =
  Message<"billing.v1.GetTransactionByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message billing.v1.GetTransactionByIdRequest.
 * Use `create(GetTransactionByIdRequestSchema)` to create a new message.
 */
export const GetTransactionByIdRequestSchema: GenMessage<GetTransactionByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_billing_v1_transactions, 6);

/**
 * @generated from message billing.v1.GetTransactionByIdResponse
 */
export type GetTransactionByIdResponse =
  Message<"billing.v1.GetTransactionByIdResponse"> & {
    /**
     * @generated from field: billing.v1.Transaction data = 2;
     */
    data?: Transaction;
  };

/**
 * Describes the message billing.v1.GetTransactionByIdResponse.
 * Use `create(GetTransactionByIdResponseSchema)` to create a new message.
 */
export const GetTransactionByIdResponseSchema: GenMessage<GetTransactionByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_billing_v1_transactions, 7);

/**
 * @generated from service billing.v1.TransactionService
 */
export const TransactionService: GenService<{
  /**
   * @generated from rpc billing.v1.TransactionService.AddTransaction
   */
  addTransaction: {
    methodKind: "unary";
    input: typeof AddTransactionRequestSchema;
    output: typeof AddTransactionResponseSchema;
  };
  /**
   * @generated from rpc billing.v1.TransactionService.GetAllTransactions
   */
  getAllTransactions: {
    methodKind: "unary";
    input: typeof GetAllTransactionsRequestSchema;
    output: typeof GetAllTransactionsResponseSchema;
  };
  /**
   * @generated from rpc billing.v1.TransactionService.GetAllTransactionsOfUser
   */
  getAllTransactionsOfUser: {
    methodKind: "unary";
    input: typeof GetAllTransactionsOfUserRequestSchema;
    output: typeof GetAllTransactionsResponseSchema;
  };
  /**
   * @generated from rpc billing.v1.TransactionService.GetTransactionById
   */
  getTransactionById: {
    methodKind: "unary";
    input: typeof GetTransactionByIdRequestSchema;
    output: typeof GetTransactionByIdResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_billing_v1_transactions, 0);
