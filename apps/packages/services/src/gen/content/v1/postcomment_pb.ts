// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file content/v1/postcomment.proto (package api.content.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type {
  PaginationDetails,
  PaginationRequest,
} from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import type { Profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";

/**
 * Describes the file content/v1/postcomment.proto.
 */
export const file_content_v1_postcomment: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.content.v1.AddPostCommentRequest
 */
export type AddPostCommentRequest =
  Message<"api.content.v1.AddPostCommentRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string content = 1;
     */
    content: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 post_id = 2;
     */
    postId: bigint;

    /**
     * @generated from field: optional int64 parent_id = 4;
     */
    parentId?: bigint;

    /**
     * @generated from field: optional bool as_vtuber = 5;
     */
    asVtuber?: boolean;
  };

/**
 * Describes the message api.content.v1.AddPostCommentRequest.
 * Use `create(AddPostCommentRequestSchema)` to create a new message.
 */
export const AddPostCommentRequestSchema: GenMessage<AddPostCommentRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postcomment, 0);

/**
 * @generated from message api.content.v1.AddPostCommentResponse
 */
export type AddPostCommentResponse =
  Message<"api.content.v1.AddPostCommentResponse"> & {
    /**
     * @generated from field: api.content.v1.PostComment data = 1;
     */
    data?: PostComment;
  };

/**
 * Describes the message api.content.v1.AddPostCommentResponse.
 * Use `create(AddPostCommentResponseSchema)` to create a new message.
 */
export const AddPostCommentResponseSchema: GenMessage<AddPostCommentResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postcomment, 1);

/**
 * @generated from message api.content.v1.GetAllPostCommentsRequest
 */
export type GetAllPostCommentsRequest =
  Message<"api.content.v1.GetAllPostCommentsRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string post_id = 1;
     */
    postId: string;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.content.v1.GetAllPostCommentsRequest.
 * Use `create(GetAllPostCommentsRequestSchema)` to create a new message.
 */
export const GetAllPostCommentsRequestSchema: GenMessage<GetAllPostCommentsRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postcomment, 2);

/**
 * @generated from message api.content.v1.GetAllPostCommentsResponse
 */
export type GetAllPostCommentsResponse =
  Message<"api.content.v1.GetAllPostCommentsResponse"> & {
    /**
     * @generated from field: repeated api.content.v1.PostComment data = 1;
     */
    data: PostComment[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.content.v1.GetAllPostCommentsResponse.
 * Use `create(GetAllPostCommentsResponseSchema)` to create a new message.
 */
export const GetAllPostCommentsResponseSchema: GenMessage<GetAllPostCommentsResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postcomment, 3);

/**
 * @generated from message api.content.v1.PostComment
 */
export type PostComment = Message<"api.content.v1.PostComment"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string content = 2;
   */
  content: string;

  /**
   * @generated from field: int64 post_id = 3;
   */
  postId: bigint;

  /**
   * @generated from field: optional int64 parent_id = 5;
   */
  parentId?: bigint;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 6;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: bool has_reply = 7;
   */
  hasReply: boolean;

  /**
   * @generated from field: api.shared.v1.Profile user = 8;
   */
  user?: Profile;

  /**
   * @generated from field: optional api.shared.v1.Profile vtuber = 9;
   */
  vtuber?: Profile;
};

/**
 * Describes the message api.content.v1.PostComment.
 * Use `create(PostCommentSchema)` to create a new message.
 */
export const PostCommentSchema: GenMessage<PostComment> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postcomment, 4);

/**
 * @generated from message api.content.v1.GetPostCommentByIdRequest
 */
export type GetPostCommentByIdRequest =
  Message<"api.content.v1.GetPostCommentByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message api.content.v1.GetPostCommentByIdRequest.
 * Use `create(GetPostCommentByIdRequestSchema)` to create a new message.
 */
export const GetPostCommentByIdRequestSchema: GenMessage<GetPostCommentByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postcomment, 5);

/**
 * @generated from message api.content.v1.GetPostRepliesOfCommentRequest
 */
export type GetPostRepliesOfCommentRequest =
  Message<"api.content.v1.GetPostRepliesOfCommentRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.content.v1.GetPostRepliesOfCommentRequest.
 * Use `create(GetPostRepliesOfCommentRequestSchema)` to create a new message.
 */
export const GetPostRepliesOfCommentRequestSchema: GenMessage<GetPostRepliesOfCommentRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postcomment, 6);

/**
 * @generated from message api.content.v1.GetPostCommentByIdResponse
 */
export type GetPostCommentByIdResponse =
  Message<"api.content.v1.GetPostCommentByIdResponse"> & {
    /**
     * @generated from field: api.content.v1.PostComment data = 1;
     */
    data?: PostComment;
  };

/**
 * Describes the message api.content.v1.GetPostCommentByIdResponse.
 * Use `create(GetPostCommentByIdResponseSchema)` to create a new message.
 */
export const GetPostCommentByIdResponseSchema: GenMessage<GetPostCommentByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postcomment, 7);

/**
 * @generated from message api.content.v1.DeletePostCommentByIdRequest
 */
export type DeletePostCommentByIdRequest =
  Message<"api.content.v1.DeletePostCommentByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message api.content.v1.DeletePostCommentByIdRequest.
 * Use `create(DeletePostCommentByIdRequestSchema)` to create a new message.
 */
export const DeletePostCommentByIdRequestSchema: GenMessage<DeletePostCommentByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postcomment, 8);

/**
 * @generated from message api.content.v1.UpdatePostCommentByIdRequest
 */
export type UpdatePostCommentByIdRequest =
  Message<"api.content.v1.UpdatePostCommentByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string content = 1;
     */
    content: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 2;
     */
    id: bigint;
  };

/**
 * Describes the message api.content.v1.UpdatePostCommentByIdRequest.
 * Use `create(UpdatePostCommentByIdRequestSchema)` to create a new message.
 */
export const UpdatePostCommentByIdRequestSchema: GenMessage<UpdatePostCommentByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postcomment, 9);

/**
 * @generated from message api.content.v1.DeletePostCommentByIdResponse
 */
export type DeletePostCommentByIdResponse =
  Message<"api.content.v1.DeletePostCommentByIdResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.content.v1.DeletePostCommentByIdResponse.
 * Use `create(DeletePostCommentByIdResponseSchema)` to create a new message.
 */
export const DeletePostCommentByIdResponseSchema: GenMessage<DeletePostCommentByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postcomment, 10);

/**
 * @generated from message api.content.v1.UpdatePostCommentByIdResponse
 */
export type UpdatePostCommentByIdResponse =
  Message<"api.content.v1.UpdatePostCommentByIdResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.content.v1.UpdatePostCommentByIdResponse.
 * Use `create(UpdatePostCommentByIdResponseSchema)` to create a new message.
 */
export const UpdatePostCommentByIdResponseSchema: GenMessage<UpdatePostCommentByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_postcomment, 11);

/**
 * @generated from service api.content.v1.PostCommentService
 */
export const PostCommentService: GenService<{
  /**
   * @generated from rpc api.content.v1.PostCommentService.AddPostComment
   */
  addPostComment: {
    methodKind: "unary";
    input: typeof AddPostCommentRequestSchema;
    output: typeof AddPostCommentResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostCommentService.GetAllPostComments
   */
  getAllPostComments: {
    methodKind: "unary";
    input: typeof GetAllPostCommentsRequestSchema;
    output: typeof GetAllPostCommentsResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostCommentService.GetAllReplesOfComment
   */
  getAllReplesOfComment: {
    methodKind: "unary";
    input: typeof GetPostRepliesOfCommentRequestSchema;
    output: typeof GetAllPostCommentsResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostCommentService.GetPostCommentById
   */
  getPostCommentById: {
    methodKind: "unary";
    input: typeof GetPostCommentByIdRequestSchema;
    output: typeof GetPostCommentByIdResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostCommentService.DeletePostCommentById
   */
  deletePostCommentById: {
    methodKind: "unary";
    input: typeof DeletePostCommentByIdRequestSchema;
    output: typeof DeletePostCommentByIdResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostCommentService.UpdatePostCommentById
   */
  updatePostCommentById: {
    methodKind: "unary";
    input: typeof UpdatePostCommentByIdRequestSchema;
    output: typeof UpdatePostCommentByIdResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_content_v1_postcomment, 0);
