// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file content/v1/posts.proto (package api.content.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type {
  PaginationDetails,
  PaginationRequest,
} from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import type { Profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";

/**
 * Describes the file content/v1/posts.proto.
 */
export const file_content_v1_posts: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.content.v1.AddPostRequest
 */
export type AddPostRequest = Message<"api.content.v1.AddPostRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: optional string media = 3;
   */
  media?: string;

  /**
   * @gotag: validate:"omitempty,oneof=picture video"
   *
   * @generated from field: optional string media_type = 4;
   */
  mediaType?: string;

  /**
   * @generated from field: string name = 5;
   */
  name: string;

  /**
   * @generated from field: bool membership_only = 6;
   */
  membershipOnly: boolean;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 category_id = 7;
   */
  categoryId: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string short_description = 8;
   */
  shortDescription: string;

  /**
   * @generated from field: optional int64 campaign_id = 9;
   */
  campaignId?: bigint;
};

/**
 * Describes the message api.content.v1.AddPostRequest.
 * Use `create(AddPostRequestSchema)` to create a new message.
 */
export const AddPostRequestSchema: GenMessage<AddPostRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 0);

/**
 * @generated from message api.content.v1.AddPostResponse
 */
export type AddPostResponse = Message<"api.content.v1.AddPostResponse"> & {
  /**
   * @generated from field: api.content.v1.Post data = 1;
   */
  data?: Post;
};

/**
 * Describes the message api.content.v1.AddPostResponse.
 * Use `create(AddPostResponseSchema)` to create a new message.
 */
export const AddPostResponseSchema: GenMessage<AddPostResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 1);

/**
 * @generated from message api.content.v1.Post
 */
export type Post = Message<"api.content.v1.Post"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: optional string media = 4;
   */
  media?: string;

  /**
   * @generated from field: optional string media_type = 5;
   */
  mediaType?: string;

  /**
   * @generated from field: string name = 6;
   */
  name: string;

  /**
   * @generated from field: bool membership_only = 7;
   */
  membershipOnly: boolean;

  /**
   * @generated from field: int64 category_id = 8;
   */
  categoryId: bigint;

  /**
   * @generated from field: api.shared.v1.Profile vtuber = 9;
   */
  vtuber?: Profile;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 10;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: string short_description = 11;
   */
  shortDescription: string;

  /**
   * @generated from field: int64 post_likes = 12;
   */
  postLikes: bigint;

  /**
   * @generated from field: bool has_liked = 13;
   */
  hasLiked: boolean;

  /**
   * @generated from field: int64 post_comments = 14;
   */
  postComments: bigint;

  /**
   * @generated from field: string slug = 15;
   */
  slug: string;

  /**
   * @generated from field: optional int64 campaign_id = 16;
   */
  campaignId?: bigint;
};

/**
 * Describes the message api.content.v1.Post.
 * Use `create(PostSchema)` to create a new message.
 */
export const PostSchema: GenMessage<Post> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 2);

/**
 * @generated from message api.content.v1.GetAllPostsRequest
 */
export type GetAllPostsRequest =
  Message<"api.content.v1.GetAllPostsRequest"> & {
    /**
     * @generated from field: optional int64 vtuber_id = 1;
     */
    vtuberId?: bigint;

    /**
     * @generated from field: optional int64 category_id = 2;
     */
    categoryId?: bigint;

    /**
     * @generated from field: optional int64 campaign_id = 3;
     */
    campaignId?: bigint;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 4;
     */
    pagination?: PaginationRequest;

    /**
     * @generated from field: optional string vtuber_username = 5;
     */
    vtuberUsername?: string;
  };

/**
 * Describes the message api.content.v1.GetAllPostsRequest.
 * Use `create(GetAllPostsRequestSchema)` to create a new message.
 */
export const GetAllPostsRequestSchema: GenMessage<GetAllPostsRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 3);

/**
 * @generated from message api.content.v1.GetAllPostsResponse
 */
export type GetAllPostsResponse =
  Message<"api.content.v1.GetAllPostsResponse"> & {
    /**
     * @generated from field: repeated api.content.v1.Post data = 1;
     */
    data: Post[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.content.v1.GetAllPostsResponse.
 * Use `create(GetAllPostsResponseSchema)` to create a new message.
 */
export const GetAllPostsResponseSchema: GenMessage<GetAllPostsResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 4);

/**
 * @generated from message api.content.v1.GetPostByIdRequest
 */
export type GetPostByIdRequest =
  Message<"api.content.v1.GetPostByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;
  };

/**
 * Describes the message api.content.v1.GetPostByIdRequest.
 * Use `create(GetPostByIdRequestSchema)` to create a new message.
 */
export const GetPostByIdRequestSchema: GenMessage<GetPostByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 5);

/**
 * @generated from message api.content.v1.GetPostByIdResponse
 */
export type GetPostByIdResponse =
  Message<"api.content.v1.GetPostByIdResponse"> & {
    /**
     * @generated from field: api.content.v1.Post data = 1;
     */
    data?: Post;
  };

/**
 * Describes the message api.content.v1.GetPostByIdResponse.
 * Use `create(GetPostByIdResponseSchema)` to create a new message.
 */
export const GetPostByIdResponseSchema: GenMessage<GetPostByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 6);

/**
 * @generated from message api.content.v1.DeletePostByIdRequest
 */
export type DeletePostByIdRequest =
  Message<"api.content.v1.DeletePostByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message api.content.v1.DeletePostByIdRequest.
 * Use `create(DeletePostByIdRequestSchema)` to create a new message.
 */
export const DeletePostByIdRequestSchema: GenMessage<DeletePostByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 7);

/**
 * @generated from message api.content.v1.GetMyPostsRequest
 */
export type GetMyPostsRequest = Message<"api.content.v1.GetMyPostsRequest"> & {
  /**
   * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
   */
  pagination?: PaginationRequest;
};

/**
 * Describes the message api.content.v1.GetMyPostsRequest.
 * Use `create(GetMyPostsRequestSchema)` to create a new message.
 */
export const GetMyPostsRequestSchema: GenMessage<GetMyPostsRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 8);

/**
 * @generated from message api.content.v1.UpdatePostByIdRequest
 */
export type UpdatePostByIdRequest =
  Message<"api.content.v1.UpdatePostByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string title = 1;
     */
    title: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string description = 2;
     */
    description: string;

    /**
     * @generated from field: optional string media = 3;
     */
    media?: string;

    /**
     * @gotag: validate:"omitempty,oneof=picture video"
     *
     * @generated from field: optional string media_type = 4;
     */
    mediaType?: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string name = 5;
     */
    name: string;

    /**
     * @generated from field: bool membership_only = 6;
     */
    membershipOnly: boolean;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 category_id = 7;
     */
    categoryId: bigint;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 9;
     */
    id: bigint;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string short_description = 10;
     */
    shortDescription: string;

    /**
     * @generated from field: optional int64 campaign_id = 11;
     */
    campaignId?: bigint;
  };

/**
 * Describes the message api.content.v1.UpdatePostByIdRequest.
 * Use `create(UpdatePostByIdRequestSchema)` to create a new message.
 */
export const UpdatePostByIdRequestSchema: GenMessage<UpdatePostByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 9);

/**
 * @generated from message api.content.v1.GetVtuberGalleryRequest
 */
export type GetVtuberGalleryRequest =
  Message<"api.content.v1.GetVtuberGalleryRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 vtuber_id = 1;
     */
    vtuberId: bigint;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.content.v1.GetVtuberGalleryRequest.
 * Use `create(GetVtuberGalleryRequestSchema)` to create a new message.
 */
export const GetVtuberGalleryRequestSchema: GenMessage<GetVtuberGalleryRequest> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 10);

/**
 * @generated from message api.content.v1.DeletePostByIdResponse
 */
export type DeletePostByIdResponse =
  Message<"api.content.v1.DeletePostByIdResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.content.v1.DeletePostByIdResponse.
 * Use `create(DeletePostByIdResponseSchema)` to create a new message.
 */
export const DeletePostByIdResponseSchema: GenMessage<DeletePostByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 11);

/**
 * @generated from message api.content.v1.UpdatePostByIdResponse
 */
export type UpdatePostByIdResponse =
  Message<"api.content.v1.UpdatePostByIdResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.content.v1.UpdatePostByIdResponse.
 * Use `create(UpdatePostByIdResponseSchema)` to create a new message.
 */
export const UpdatePostByIdResponseSchema: GenMessage<UpdatePostByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_content_v1_posts, 12);

/**
 * @generated from service api.content.v1.PostService
 */
export const PostService: GenService<{
  /**
   * @generated from rpc api.content.v1.PostService.AddPost
   */
  addPost: {
    methodKind: "unary";
    input: typeof AddPostRequestSchema;
    output: typeof AddPostResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostService.GetAllPosts
   */
  getAllPosts: {
    methodKind: "unary";
    input: typeof GetAllPostsRequestSchema;
    output: typeof GetAllPostsResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostService.GetPostById
   */
  getPostById: {
    methodKind: "unary";
    input: typeof GetPostByIdRequestSchema;
    output: typeof GetPostByIdResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostService.DeletePostById
   */
  deletePostById: {
    methodKind: "unary";
    input: typeof DeletePostByIdRequestSchema;
    output: typeof DeletePostByIdResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostService.GetMyPosts
   */
  getMyPosts: {
    methodKind: "unary";
    input: typeof GetMyPostsRequestSchema;
    output: typeof GetAllPostsResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostService.UpdatePostById
   */
  updatePostById: {
    methodKind: "unary";
    input: typeof UpdatePostByIdRequestSchema;
    output: typeof UpdatePostByIdResponseSchema;
  };
  /**
   * @generated from rpc api.content.v1.PostService.GetVtuberGalleries
   */
  getVtuberGalleries: {
    methodKind: "unary";
    input: typeof GetVtuberGalleryRequestSchema;
    output: typeof GetAllPostsResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_content_v1_posts, 0);
