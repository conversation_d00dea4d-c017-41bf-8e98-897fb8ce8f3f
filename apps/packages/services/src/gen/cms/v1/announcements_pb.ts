// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file cms/v1/announcements.proto (package api.cms.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";

/**
 * Describes the file cms/v1/announcements.proto.
 */
export const file_cms_v1_announcements: GenFile =
  /*@__PURE__*/
  fileDesc(
    "ChpjbXMvdjEvYW5ub3VuY2VtZW50cy5wcm90bxIKYXBpLmNtcy52MSI8ChZBZGRBbm5vdW5jZW1lbnRSZXF1ZXN0Eg0KBWltYWdlGAEgASgJEhMKC2Rlc2NyaXB0aW9uGAIgASgJIj4KFEFubm91bmNlbWVudFJlc3BvbnNlEiYKBGRhdGEYASABKAsyGC5hcGkuY21zLnYxLkFubm91bmNlbWVudCIYChZHZXRBbm5vdW5jZW1lbnRSZXF1ZXN0InoKDEFubm91bmNlbWVudBIKCgJpZBgBIAEoAxINCgVpbWFnZRgCIAEoCRIPCgdjb250ZW50GAMgASgJEg4KBmFjdGl2ZRgEIAEoCBIuCgpjcmVhdGVkX2F0GAUgASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcCJHChlVcGRhdGVBbm5vdW5jZW1lbnRSZXF1ZXN0EgoKAmlkGAEgASgDEg0KBWltYWdlGAIgASgJEg8KB2NvbnRlbnQYAyABKAkiJwoZVG9nZ2xlQW5ub3VuY2VtZW50UmVxdWVzdBIKCgJpZBgBIAEoAzKmAwoUQW5ub3VuY2VtZW50c1NlcnZpY2USYQoPQWRkQW5ub3VuY2VtZW50EiIuYXBpLmNtcy52MS5BZGRBbm5vdW5jZW1lbnRSZXF1ZXN0GiAuYXBpLmNtcy52MS5Bbm5vdW5jZW1lbnRSZXNwb25zZSIIgrUYBAgBEAESZwoSVXBkYXRlQW5ub3VuY2VtZW50EiUuYXBpLmNtcy52MS5VcGRhdGVBbm5vdW5jZW1lbnRSZXF1ZXN0GiAuYXBpLmNtcy52MS5Bbm5vdW5jZW1lbnRSZXNwb25zZSIIgrUYBAgBEAESZwoSVG9nZ2xlQW5ub3VuY2VtZW50EiUuYXBpLmNtcy52MS5Ub2dnbGVBbm5vdW5jZW1lbnRSZXF1ZXN0GiAuYXBpLmNtcy52MS5Bbm5vdW5jZW1lbnRSZXNwb25zZSIIgrUYBAgBEAESWQoPR2V0QW5ub3VuY2VtZW50EiIuYXBpLmNtcy52MS5HZXRBbm5vdW5jZW1lbnRSZXF1ZXN0GiAuYXBpLmNtcy52MS5Bbm5vdW5jZW1lbnRSZXNwb25zZSIAQixaKmdpdGh1Yi5jb20vbnNwLWluYy92dHViZXIvYXBpL2Ntcy92MTtjbXN2MWIGcHJvdG8z",
    [file_authz_v1_authz, file_google_protobuf_timestamp],
  );

/**
 * @generated from message api.cms.v1.AddAnnouncementRequest
 */
export type AddAnnouncementRequest =
  Message<"api.cms.v1.AddAnnouncementRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string image = 1;
     */
    image: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string description = 2;
     */
    description: string;
  };

/**
 * Describes the message api.cms.v1.AddAnnouncementRequest.
 * Use `create(AddAnnouncementRequestSchema)` to create a new message.
 */
export const AddAnnouncementRequestSchema: GenMessage<AddAnnouncementRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_announcements, 0);

/**
 * @generated from message api.cms.v1.AnnouncementResponse
 */
export type AnnouncementResponse =
  Message<"api.cms.v1.AnnouncementResponse"> & {
    /**
     * @generated from field: api.cms.v1.Announcement data = 1;
     */
    data?: Announcement;
  };

/**
 * Describes the message api.cms.v1.AnnouncementResponse.
 * Use `create(AnnouncementResponseSchema)` to create a new message.
 */
export const AnnouncementResponseSchema: GenMessage<AnnouncementResponse> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_announcements, 1);

/**
 * @generated from message api.cms.v1.GetAnnouncementRequest
 */
export type GetAnnouncementRequest =
  Message<"api.cms.v1.GetAnnouncementRequest"> & {};

/**
 * Describes the message api.cms.v1.GetAnnouncementRequest.
 * Use `create(GetAnnouncementRequestSchema)` to create a new message.
 */
export const GetAnnouncementRequestSchema: GenMessage<GetAnnouncementRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_announcements, 2);

/**
 * @generated from message api.cms.v1.Announcement
 */
export type Announcement = Message<"api.cms.v1.Announcement"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string image = 2;
   */
  image: string;

  /**
   * @generated from field: string content = 3;
   */
  content: string;

  /**
   * @generated from field: bool active = 4;
   */
  active: boolean;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 5;
   */
  createdAt?: Timestamp;
};

/**
 * Describes the message api.cms.v1.Announcement.
 * Use `create(AnnouncementSchema)` to create a new message.
 */
export const AnnouncementSchema: GenMessage<Announcement> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_announcements, 3);

/**
 * @generated from message api.cms.v1.UpdateAnnouncementRequest
 */
export type UpdateAnnouncementRequest =
  Message<"api.cms.v1.UpdateAnnouncementRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string image = 2;
     */
    image: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string content = 3;
     */
    content: string;
  };

/**
 * Describes the message api.cms.v1.UpdateAnnouncementRequest.
 * Use `create(UpdateAnnouncementRequestSchema)` to create a new message.
 */
export const UpdateAnnouncementRequestSchema: GenMessage<UpdateAnnouncementRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_announcements, 4);

/**
 * @generated from message api.cms.v1.ToggleAnnouncementRequest
 */
export type ToggleAnnouncementRequest =
  Message<"api.cms.v1.ToggleAnnouncementRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message api.cms.v1.ToggleAnnouncementRequest.
 * Use `create(ToggleAnnouncementRequestSchema)` to create a new message.
 */
export const ToggleAnnouncementRequestSchema: GenMessage<ToggleAnnouncementRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_announcements, 5);

/**
 * @generated from service api.cms.v1.AnnouncementsService
 */
export const AnnouncementsService: GenService<{
  /**
   * @generated from rpc api.cms.v1.AnnouncementsService.AddAnnouncement
   */
  addAnnouncement: {
    methodKind: "unary";
    input: typeof AddAnnouncementRequestSchema;
    output: typeof AnnouncementResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.AnnouncementsService.UpdateAnnouncement
   */
  updateAnnouncement: {
    methodKind: "unary";
    input: typeof UpdateAnnouncementRequestSchema;
    output: typeof AnnouncementResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.AnnouncementsService.ToggleAnnouncement
   */
  toggleAnnouncement: {
    methodKind: "unary";
    input: typeof ToggleAnnouncementRequestSchema;
    output: typeof AnnouncementResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.AnnouncementsService.GetAnnouncement
   */
  getAnnouncement: {
    methodKind: "unary";
    input: typeof GetAnnouncementRequestSchema;
    output: typeof AnnouncementResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_cms_v1_announcements, 0);
