// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file cms/v1/static.proto (package api.cms.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import type { SocialMediaLinks } from "../../shared/v1/social_media_links_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";

/**
 * Describes the file cms/v1/static.proto.
 */
export const file_cms_v1_static: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.cms.v1.UpdateStaticRequest
 */
export type UpdateStaticRequest = Message<"api.cms.v1.UpdateStaticRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string value = 2;
   */
  value: string;
};

/**
 * Describes the message api.cms.v1.UpdateStaticRequest.
 * Use `create(UpdateStaticRequestSchema)` to create a new message.
 */
export const UpdateStaticRequestSchema: GenMessage<UpdateStaticRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_static, 0);

/**
 * @generated from message api.cms.v1.AddStaticRequest
 */
export type AddStaticRequest = Message<"api.cms.v1.AddStaticRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string value = 2;
   */
  value: string;

  /**
   * @gotag: validate:"required,oneof=en-us ja-jp"
   *
   * @generated from field: string language = 3;
   */
  language: string;
};

/**
 * Describes the message api.cms.v1.AddStaticRequest.
 * Use `create(AddStaticRequestSchema)` to create a new message.
 */
export const AddStaticRequestSchema: GenMessage<AddStaticRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_static, 1);

/**
 * @generated from message api.cms.v1.DeleteStaticRequest
 */
export type DeleteStaticRequest = Message<"api.cms.v1.DeleteStaticRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.cms.v1.DeleteStaticRequest.
 * Use `create(DeleteStaticRequestSchema)` to create a new message.
 */
export const DeleteStaticRequestSchema: GenMessage<DeleteStaticRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_static, 2);

/**
 * @generated from message api.cms.v1.UpdateSocialMediaRequest
 */
export type UpdateSocialMediaRequest =
  Message<"api.cms.v1.UpdateSocialMediaRequest"> & {
    /**
     * @generated from field: api.shared.v1.SocialMediaLinks social_media_links = 1;
     */
    socialMediaLinks?: SocialMediaLinks;
  };

/**
 * Describes the message api.cms.v1.UpdateSocialMediaRequest.
 * Use `create(UpdateSocialMediaRequestSchema)` to create a new message.
 */
export const UpdateSocialMediaRequestSchema: GenMessage<UpdateSocialMediaRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_static, 3);

/**
 * @generated from message api.cms.v1.UpdateSocialMediaResponse
 */
export type UpdateSocialMediaResponse =
  Message<"api.cms.v1.UpdateSocialMediaResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.cms.v1.UpdateSocialMediaResponse.
 * Use `create(UpdateSocialMediaResponseSchema)` to create a new message.
 */
export const UpdateSocialMediaResponseSchema: GenMessage<UpdateSocialMediaResponse> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_static, 4);

/**
 * @generated from message api.cms.v1.GetSocialMediaLinksResponse
 */
export type GetSocialMediaLinksResponse =
  Message<"api.cms.v1.GetSocialMediaLinksResponse"> & {
    /**
     * @generated from field: api.shared.v1.SocialMediaLinks social_media_links = 1;
     */
    socialMediaLinks?: SocialMediaLinks;
  };

/**
 * Describes the message api.cms.v1.GetSocialMediaLinksResponse.
 * Use `create(GetSocialMediaLinksResponseSchema)` to create a new message.
 */
export const GetSocialMediaLinksResponseSchema: GenMessage<GetSocialMediaLinksResponse> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_static, 5);

/**
 * @generated from message api.cms.v1.GetSocialMediaLinksRequest
 */
export type GetSocialMediaLinksRequest =
  Message<"api.cms.v1.GetSocialMediaLinksRequest"> & {};

/**
 * Describes the message api.cms.v1.GetSocialMediaLinksRequest.
 * Use `create(GetSocialMediaLinksRequestSchema)` to create a new message.
 */
export const GetSocialMediaLinksRequestSchema: GenMessage<GetSocialMediaLinksRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_static, 6);

/**
 * @generated from message api.cms.v1.StaticResponse
 */
export type StaticResponse = Message<"api.cms.v1.StaticResponse"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string key = 2;
   */
  key: string;

  /**
   * @generated from field: string value = 3;
   */
  value: string;

  /**
   * @generated from field: string language = 4;
   */
  language: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 5;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 6;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message api.cms.v1.StaticResponse.
 * Use `create(StaticResponseSchema)` to create a new message.
 */
export const StaticResponseSchema: GenMessage<StaticResponse> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_static, 7);

/**
 * @generated from message api.cms.v1.GetAllStaticRequest
 */
export type GetAllStaticRequest = Message<"api.cms.v1.GetAllStaticRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string key = 1;
   */
  key: string;
};

/**
 * Describes the message api.cms.v1.GetAllStaticRequest.
 * Use `create(GetAllStaticRequestSchema)` to create a new message.
 */
export const GetAllStaticRequestSchema: GenMessage<GetAllStaticRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_static, 8);

/**
 * @generated from message api.cms.v1.GetAllStaticResponse
 */
export type GetAllStaticResponse =
  Message<"api.cms.v1.GetAllStaticResponse"> & {
    /**
     * @generated from field: repeated api.cms.v1.StaticResponse data = 1;
     */
    data: StaticResponse[];
  };

/**
 * Describes the message api.cms.v1.GetAllStaticResponse.
 * Use `create(GetAllStaticResponseSchema)` to create a new message.
 */
export const GetAllStaticResponseSchema: GenMessage<GetAllStaticResponse> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_static, 9);

/**
 * @generated from message api.cms.v1.DeleteStaticResponse
 */
export type DeleteStaticResponse =
  Message<"api.cms.v1.DeleteStaticResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.cms.v1.DeleteStaticResponse.
 * Use `create(DeleteStaticResponseSchema)` to create a new message.
 */
export const DeleteStaticResponseSchema: GenMessage<DeleteStaticResponse> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_static, 10);

/**
 * @generated from service api.cms.v1.StaticService
 */
export const StaticService: GenService<{
  /**
   * @generated from rpc api.cms.v1.StaticService.UpdateStaticResource
   */
  updateStaticResource: {
    methodKind: "unary";
    input: typeof UpdateStaticRequestSchema;
    output: typeof StaticResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.StaticService.GetAllStaticResource
   */
  getAllStaticResource: {
    methodKind: "unary";
    input: typeof GetAllStaticRequestSchema;
    output: typeof GetAllStaticResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.StaticService.AddStaticResource
   */
  addStaticResource: {
    methodKind: "unary";
    input: typeof AddStaticRequestSchema;
    output: typeof StaticResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.StaticService.DeleteStaticResource
   */
  deleteStaticResource: {
    methodKind: "unary";
    input: typeof DeleteStaticRequestSchema;
    output: typeof DeleteStaticResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.StaticService.UpdateSocialMedia
   */
  updateSocialMedia: {
    methodKind: "unary";
    input: typeof UpdateSocialMediaRequestSchema;
    output: typeof UpdateSocialMediaResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.StaticService.GetSocialMediaLinks
   */
  getSocialMediaLinks: {
    methodKind: "unary";
    input: typeof GetSocialMediaLinksRequestSchema;
    output: typeof GetSocialMediaLinksResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_cms_v1_static, 0);
