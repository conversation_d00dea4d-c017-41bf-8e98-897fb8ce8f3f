// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file cms/v1/faqs.proto (package api.cms.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";

/**
 * Describes the file cms/v1/faqs.proto.
 */
export const file_cms_v1_faqs: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.cms.v1.AddFaqRequest
 */
export type AddFaqRequest = Message<"api.cms.v1.AddFaqRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string question = 1;
   */
  question: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string response = 2;
   */
  response: string;

  /**
   * @gotag: validate:"gte=0"
   *
   * @generated from field: int32 index = 3;
   */
  index: number;

  /**
   * @generated from field: optional bool is_active = 4;
   */
  isActive?: boolean;

  /**
   * @gotag: validate:"omitempty,oneof=en-us ja-jp"
   *
   * @generated from field: string language = 5;
   */
  language: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string tag = 6;
   */
  tag: string;
};

/**
 * Describes the message api.cms.v1.AddFaqRequest.
 * Use `create(AddFaqRequestSchema)` to create a new message.
 */
export const AddFaqRequestSchema: GenMessage<AddFaqRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 0);

/**
 * @generated from message api.cms.v1.AddFaqResponse
 */
export type AddFaqResponse = Message<"api.cms.v1.AddFaqResponse"> & {
  /**
   * @generated from field: api.cms.v1.Faq data = 1;
   */
  data?: Faq;
};

/**
 * Describes the message api.cms.v1.AddFaqResponse.
 * Use `create(AddFaqResponseSchema)` to create a new message.
 */
export const AddFaqResponseSchema: GenMessage<AddFaqResponse> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 1);

/**
 * @generated from message api.cms.v1.Faq
 */
export type Faq = Message<"api.cms.v1.Faq"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string question = 2;
   */
  question: string;

  /**
   * @generated from field: string response = 3;
   */
  response: string;

  /**
   * @generated from field: int32 index = 4;
   */
  index: number;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 5;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: bool is_active = 6;
   */
  isActive: boolean;

  /**
   * @generated from field: string language = 7;
   */
  language: string;

  /**
   * @generated from field: string tag = 8;
   */
  tag: string;
};

/**
 * Describes the message api.cms.v1.Faq.
 * Use `create(FaqSchema)` to create a new message.
 */
export const FaqSchema: GenMessage<Faq> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 2);

/**
 * @generated from message api.cms.v1.GetAllFaqsRequest
 */
export type GetAllFaqsRequest = Message<"api.cms.v1.GetAllFaqsRequest"> & {
  /**
   * @generated from field: optional string language = 1;
   */
  language?: string;
};

/**
 * Describes the message api.cms.v1.GetAllFaqsRequest.
 * Use `create(GetAllFaqsRequestSchema)` to create a new message.
 */
export const GetAllFaqsRequestSchema: GenMessage<GetAllFaqsRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 3);

/**
 * @generated from message api.cms.v1.GetAllFaqsResponse
 */
export type GetAllFaqsResponse = Message<"api.cms.v1.GetAllFaqsResponse"> & {
  /**
   * @generated from field: repeated api.cms.v1.Faq data = 1;
   */
  data: Faq[];
};

/**
 * Describes the message api.cms.v1.GetAllFaqsResponse.
 * Use `create(GetAllFaqsResponseSchema)` to create a new message.
 */
export const GetAllFaqsResponseSchema: GenMessage<GetAllFaqsResponse> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 4);

/**
 * @generated from message api.cms.v1.GetFaqRequest
 */
export type GetFaqRequest = Message<"api.cms.v1.GetFaqRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.cms.v1.GetFaqRequest.
 * Use `create(GetFaqRequestSchema)` to create a new message.
 */
export const GetFaqRequestSchema: GenMessage<GetFaqRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 5);

/**
 * @generated from message api.cms.v1.GetFaqResponse
 */
export type GetFaqResponse = Message<"api.cms.v1.GetFaqResponse"> & {
  /**
   * @generated from field: api.cms.v1.Faq data = 1;
   */
  data?: Faq;
};

/**
 * Describes the message api.cms.v1.GetFaqResponse.
 * Use `create(GetFaqResponseSchema)` to create a new message.
 */
export const GetFaqResponseSchema: GenMessage<GetFaqResponse> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 6);

/**
 * @generated from message api.cms.v1.UpdateFaqRequest
 */
export type UpdateFaqRequest = Message<"api.cms.v1.UpdateFaqRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string question = 2;
   */
  question: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string response = 3;
   */
  response: string;

  /**
   * @gotag: validate:"gte=0"
   *
   * @generated from field: int32 index = 4;
   */
  index: number;

  /**
   * @gotag: validate:"omitempty,oneof=en-us ja-jp"
   *
   * @generated from field: string language = 5;
   */
  language: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string tag = 6;
   */
  tag: string;
};

/**
 * Describes the message api.cms.v1.UpdateFaqRequest.
 * Use `create(UpdateFaqRequestSchema)` to create a new message.
 */
export const UpdateFaqRequestSchema: GenMessage<UpdateFaqRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 7);

/**
 * @generated from message api.cms.v1.DeleteFaqRequest
 */
export type DeleteFaqRequest = Message<"api.cms.v1.DeleteFaqRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.cms.v1.DeleteFaqRequest.
 * Use `create(DeleteFaqRequestSchema)` to create a new message.
 */
export const DeleteFaqRequestSchema: GenMessage<DeleteFaqRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 8);

/**
 * @generated from message api.cms.v1.ToogleFaqStatusRequest
 */
export type ToogleFaqStatusRequest =
  Message<"api.cms.v1.ToogleFaqStatusRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message api.cms.v1.ToogleFaqStatusRequest.
 * Use `create(ToogleFaqStatusRequestSchema)` to create a new message.
 */
export const ToogleFaqStatusRequestSchema: GenMessage<ToogleFaqStatusRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 9);

/**
 * @generated from message api.cms.v1.GetAllActiveFaqsRequest
 */
export type GetAllActiveFaqsRequest =
  Message<"api.cms.v1.GetAllActiveFaqsRequest"> & {
    /**
     * @generated from field: optional string language = 1;
     */
    language?: string;
  };

/**
 * Describes the message api.cms.v1.GetAllActiveFaqsRequest.
 * Use `create(GetAllActiveFaqsRequestSchema)` to create a new message.
 */
export const GetAllActiveFaqsRequestSchema: GenMessage<GetAllActiveFaqsRequest> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 10);

/**
 * @generated from message api.cms.v1.UpdateFaqResponse
 */
export type UpdateFaqResponse = Message<"api.cms.v1.UpdateFaqResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.cms.v1.UpdateFaqResponse.
 * Use `create(UpdateFaqResponseSchema)` to create a new message.
 */
export const UpdateFaqResponseSchema: GenMessage<UpdateFaqResponse> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 11);

/**
 * @generated from message api.cms.v1.DeleteFaqResponse
 */
export type DeleteFaqResponse = Message<"api.cms.v1.DeleteFaqResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.cms.v1.DeleteFaqResponse.
 * Use `create(DeleteFaqResponseSchema)` to create a new message.
 */
export const DeleteFaqResponseSchema: GenMessage<DeleteFaqResponse> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 12);

/**
 * @generated from message api.cms.v1.ToogleFaqStatusResponse
 */
export type ToogleFaqStatusResponse =
  Message<"api.cms.v1.ToogleFaqStatusResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.cms.v1.ToogleFaqStatusResponse.
 * Use `create(ToogleFaqStatusResponseSchema)` to create a new message.
 */
export const ToogleFaqStatusResponseSchema: GenMessage<ToogleFaqStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_cms_v1_faqs, 13);

/**
 * @generated from service api.cms.v1.FaqService
 */
export const FaqService: GenService<{
  /**
   * @generated from rpc api.cms.v1.FaqService.AddFaq
   */
  addFaq: {
    methodKind: "unary";
    input: typeof AddFaqRequestSchema;
    output: typeof AddFaqResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.FaqService.GetAllFaqs
   */
  getAllFaqs: {
    methodKind: "unary";
    input: typeof GetAllFaqsRequestSchema;
    output: typeof GetAllFaqsResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.FaqService.GetFaq
   */
  getFaq: {
    methodKind: "unary";
    input: typeof GetFaqRequestSchema;
    output: typeof GetFaqResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.FaqService.UpdateFaq
   */
  updateFaq: {
    methodKind: "unary";
    input: typeof UpdateFaqRequestSchema;
    output: typeof UpdateFaqResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.FaqService.DeleteFaq
   */
  deleteFaq: {
    methodKind: "unary";
    input: typeof DeleteFaqRequestSchema;
    output: typeof DeleteFaqResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.FaqService.ToogleFaqStatus
   */
  toogleFaqStatus: {
    methodKind: "unary";
    input: typeof ToogleFaqStatusRequestSchema;
    output: typeof ToogleFaqStatusResponseSchema;
  };
  /**
   * @generated from rpc api.cms.v1.FaqService.GetAllActiveFaqs
   */
  getAllActiveFaqs: {
    methodKind: "unary";
    input: typeof GetAllActiveFaqsRequestSchema;
    output: typeof GetAllFaqsResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_cms_v1_faqs, 0);
