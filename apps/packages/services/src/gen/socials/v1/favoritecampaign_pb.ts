// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file socials/v1/favoritecampaign.proto (package api.socials.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { GenericResponseSchema } from "../../shared/v1/generic_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type {
  PaginationDetails,
  PaginationRequest,
} from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";

/**
 * Describes the file socials/v1/favoritecampaign.proto.
 */
export const file_socials_v1_favoritecampaign: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiFzb2NpYWxzL3YxL2Zhdm9yaXRlY2FtcGFpZ24ucHJvdG8SDmFwaS5zb2NpYWxzLnYxIjEKGkFkZEZhdm9yaXRlQ2FtcGFpZ25SZXF1ZXN0EhMKC2NhbXBhaWduX2lkGAEgASgDIk0KG0FkZEZhdm9yaXRlQ2FtcGFpZ25SZXNwb25zZRIuCgRkYXRhGAEgASgLMiAuYXBpLnNvY2lhbHMudjEuRmF2b3JpdGVDYW1wYWlnbiK3AQobRmF2b3JpdGVDYW1wYWlnbldpdGhEZXRhaWxzEhMKC2NhbXBhaWduX2lkGAEgASgDEg8KB3VzZXJfaWQYAiABKAMSLgoKY3JlYXRlZF9hdBgDIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXASCgoCaWQYBCABKAMSGQoRc2hvcnRfZGVzY3JpcHRpb24YBSABKAkSDAoEbmFtZRgGIAEoCRINCgVpbWFnZRgHIAEoCSKkAQoQRmF2b3JpdGVDYW1wYWlnbhIKCgJpZBgBIAEoAxITCgtjYW1wYWlnbl9pZBgCIAEoAxIPCgd1c2VyX2lkGAMgASgDEi4KCmNyZWF0ZWRfYXQYBCABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wEi4KCnVwZGF0ZWRfYXQYBSABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wImkKHUdldEFsbEZhdm9yaXRlQ2FtcGFpZ25SZXF1ZXN0EjkKCnBhZ2luYXRpb24YAyABKAsyIC5hcGkuc2hhcmVkLnYxLlBhZ2luYXRpb25SZXF1ZXN0SACIAQFCDQoLX3BhZ2luYXRpb24imQEKHkdldEFsbEZhdm9yaXRlQ2FtcGFpZ25SZXNwb25zZRI5CgRkYXRhGAEgAygLMisuYXBpLnNvY2lhbHMudjEuRmF2b3JpdGVDYW1wYWlnbldpdGhEZXRhaWxzEjwKEnBhZ2luYXRpb25fZGV0YWlscxgCIAEoCzIgLmFwaS5zaGFyZWQudjEuUGFnaW5hdGlvbkRldGFpbHMiNAodRGVsZXRlRmF2b3JpdGVDYW1wYWlnblJlcXVlc3QSEwoLY2FtcGFpZ25faWQYASABKAMygwMKF0Zhdm9yaXRlQ2FtcGFpZ25TZXJ2aWNlEnYKE0FkZEZhdm9yaXRlQ2FtcGFpZ24SKi5hcGkuc29jaWFscy52MS5BZGRGYXZvcml0ZUNhbXBhaWduUmVxdWVzdBorLmFwaS5zb2NpYWxzLnYxLkFkZEZhdm9yaXRlQ2FtcGFpZ25SZXNwb25zZSIGgrUYAggBEn8KFkdldEFsbEZhdm9yaXRlQ2FtcGFpZ24SLS5hcGkuc29jaWFscy52MS5HZXRBbGxGYXZvcml0ZUNhbXBhaWduUmVxdWVzdBouLmFwaS5zb2NpYWxzLnYxLkdldEFsbEZhdm9yaXRlQ2FtcGFpZ25SZXNwb25zZSIGgrUYAggBEm8KFkRlbGV0ZUZhdm9yaXRlQ2FtcGFpZ24SLS5hcGkuc29jaWFscy52MS5EZWxldGVGYXZvcml0ZUNhbXBhaWduUmVxdWVzdBoeLmFwaS5zaGFyZWQudjEuR2VuZXJpY1Jlc3BvbnNlIgaCtRgCCAFCNFoyZ2l0aHViLmNvbS9uc3AtaW5jL3Z0dWJlci9hcGkvc29jaWFscy92MTtzb2NpYWxzdjFiBnByb3RvMw",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.socials.v1.AddFavoriteCampaignRequest
 */
export type AddFavoriteCampaignRequest =
  Message<"api.socials.v1.AddFavoriteCampaignRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 campaign_id = 1;
     */
    campaignId: bigint;
  };

/**
 * Describes the message api.socials.v1.AddFavoriteCampaignRequest.
 * Use `create(AddFavoriteCampaignRequestSchema)` to create a new message.
 */
export const AddFavoriteCampaignRequestSchema: GenMessage<AddFavoriteCampaignRequest> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoritecampaign, 0);

/**
 * @generated from message api.socials.v1.AddFavoriteCampaignResponse
 */
export type AddFavoriteCampaignResponse =
  Message<"api.socials.v1.AddFavoriteCampaignResponse"> & {
    /**
     * @generated from field: api.socials.v1.FavoriteCampaign data = 1;
     */
    data?: FavoriteCampaign;
  };

/**
 * Describes the message api.socials.v1.AddFavoriteCampaignResponse.
 * Use `create(AddFavoriteCampaignResponseSchema)` to create a new message.
 */
export const AddFavoriteCampaignResponseSchema: GenMessage<AddFavoriteCampaignResponse> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoritecampaign, 1);

/**
 * @generated from message api.socials.v1.FavoriteCampaignWithDetails
 */
export type FavoriteCampaignWithDetails =
  Message<"api.socials.v1.FavoriteCampaignWithDetails"> & {
    /**
     * @generated from field: int64 campaign_id = 1;
     */
    campaignId: bigint;

    /**
     * @generated from field: int64 user_id = 2;
     */
    userId: bigint;

    /**
     * @generated from field: google.protobuf.Timestamp created_at = 3;
     */
    createdAt?: Timestamp;

    /**
     * @generated from field: int64 id = 4;
     */
    id: bigint;

    /**
     * @generated from field: string short_description = 5;
     */
    shortDescription: string;

    /**
     * @generated from field: string name = 6;
     */
    name: string;

    /**
     * @generated from field: string image = 7;
     */
    image: string;
  };

/**
 * Describes the message api.socials.v1.FavoriteCampaignWithDetails.
 * Use `create(FavoriteCampaignWithDetailsSchema)` to create a new message.
 */
export const FavoriteCampaignWithDetailsSchema: GenMessage<FavoriteCampaignWithDetails> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoritecampaign, 2);

/**
 * @generated from message api.socials.v1.FavoriteCampaign
 */
export type FavoriteCampaign = Message<"api.socials.v1.FavoriteCampaign"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: int64 campaign_id = 2;
   */
  campaignId: bigint;

  /**
   * @generated from field: int64 user_id = 3;
   */
  userId: bigint;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 4;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 5;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message api.socials.v1.FavoriteCampaign.
 * Use `create(FavoriteCampaignSchema)` to create a new message.
 */
export const FavoriteCampaignSchema: GenMessage<FavoriteCampaign> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoritecampaign, 3);

/**
 * @generated from message api.socials.v1.GetAllFavoriteCampaignRequest
 */
export type GetAllFavoriteCampaignRequest =
  Message<"api.socials.v1.GetAllFavoriteCampaignRequest"> & {
    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 3;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.socials.v1.GetAllFavoriteCampaignRequest.
 * Use `create(GetAllFavoriteCampaignRequestSchema)` to create a new message.
 */
export const GetAllFavoriteCampaignRequestSchema: GenMessage<GetAllFavoriteCampaignRequest> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoritecampaign, 4);

/**
 * @generated from message api.socials.v1.GetAllFavoriteCampaignResponse
 */
export type GetAllFavoriteCampaignResponse =
  Message<"api.socials.v1.GetAllFavoriteCampaignResponse"> & {
    /**
     * @generated from field: repeated api.socials.v1.FavoriteCampaignWithDetails data = 1;
     */
    data: FavoriteCampaignWithDetails[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.socials.v1.GetAllFavoriteCampaignResponse.
 * Use `create(GetAllFavoriteCampaignResponseSchema)` to create a new message.
 */
export const GetAllFavoriteCampaignResponseSchema: GenMessage<GetAllFavoriteCampaignResponse> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoritecampaign, 5);

/**
 * @generated from message api.socials.v1.DeleteFavoriteCampaignRequest
 */
export type DeleteFavoriteCampaignRequest =
  Message<"api.socials.v1.DeleteFavoriteCampaignRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 campaign_id = 1;
     */
    campaignId: bigint;
  };

/**
 * Describes the message api.socials.v1.DeleteFavoriteCampaignRequest.
 * Use `create(DeleteFavoriteCampaignRequestSchema)` to create a new message.
 */
export const DeleteFavoriteCampaignRequestSchema: GenMessage<DeleteFavoriteCampaignRequest> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoritecampaign, 6);

/**
 * @generated from service api.socials.v1.FavoriteCampaignService
 */
export const FavoriteCampaignService: GenService<{
  /**
   * @generated from rpc api.socials.v1.FavoriteCampaignService.AddFavoriteCampaign
   */
  addFavoriteCampaign: {
    methodKind: "unary";
    input: typeof AddFavoriteCampaignRequestSchema;
    output: typeof AddFavoriteCampaignResponseSchema;
  };
  /**
   * @generated from rpc api.socials.v1.FavoriteCampaignService.GetAllFavoriteCampaign
   */
  getAllFavoriteCampaign: {
    methodKind: "unary";
    input: typeof GetAllFavoriteCampaignRequestSchema;
    output: typeof GetAllFavoriteCampaignResponseSchema;
  };
  /**
   * @generated from rpc api.socials.v1.FavoriteCampaignService.DeleteFavoriteCampaign
   */
  deleteFavoriteCampaign: {
    methodKind: "unary";
    input: typeof DeleteFavoriteCampaignRequestSchema;
    output: typeof GenericResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_socials_v1_favoritecampaign, 0);
