// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file campaigns/v1/campaigns.proto (package api.campaigns.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type {
  PaginationDetails,
  PaginationRequest,
} from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import type { Profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import type { SocialMediaLinks } from "../../shared/v1/social_media_links_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";
import type { VtuberProfile } from "../../vtubers/v1/vtuberprofiles_pb";
import { file_vtubers_v1_vtuberprofiles } from "../../vtubers/v1/vtuberprofiles_pb";
import type { CampaignBanner } from "./campaignbanner_pb";
import { file_campaigns_v1_campaignbanner } from "./campaignbanner_pb";

/**
 * Describes the file campaigns/v1/campaigns.proto.
 */
export const file_campaigns_v1_campaigns: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_authz_v1_authz,
      file_campaigns_v1_campaignbanner,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
      file_vtubers_v1_vtuberprofiles,
    ],
  );

/**
 * @generated from message api.campaigns.v1.Subscription
 */
export type Subscription = Message<"api.campaigns.v1.Subscription"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string image = 4;
   */
  image: string;

  /**
   * @generated from field: optional string comment = 5;
   */
  comment?: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 6;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: int64 campaign_id = 7;
   */
  campaignId: bigint;

  /**
   * @generated from field: int32 amount = 8;
   */
  amount: number;

  /**
   * @generated from field: int64 campaign_variant_id = 9;
   */
  campaignVariantId: bigint;

  /**
   * @generated from field: string campaign_variant_title = 10;
   */
  campaignVariantTitle: string;

  /**
   * @generated from field: string campaign_variant_image = 11;
   */
  campaignVariantImage: string;
};

/**
 * Describes the message api.campaigns.v1.Subscription.
 * Use `create(SubscriptionSchema)` to create a new message.
 */
export const SubscriptionSchema: GenMessage<Subscription> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 0);

/**
 * @generated from message api.campaigns.v1.SubscriberResponse
 */
export type SubscriberResponse =
  Message<"api.campaigns.v1.SubscriberResponse"> & {
    /**
     * @generated from field: repeated api.campaigns.v1.Subscription data = 1;
     */
    data: Subscription[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.campaigns.v1.SubscriberResponse.
 * Use `create(SubscriberResponseSchema)` to create a new message.
 */
export const SubscriberResponseSchema: GenMessage<SubscriberResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 1);

/**
 * @generated from message api.campaigns.v1.SubscriberRequest
 */
export type SubscriberRequest =
  Message<"api.campaigns.v1.SubscriberRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 campaign_id = 1;
     */
    campaignId: bigint;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.campaigns.v1.SubscriberRequest.
 * Use `create(SubscriberRequestSchema)` to create a new message.
 */
export const SubscriberRequestSchema: GenMessage<SubscriberRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 2);

/**
 * @generated from message api.campaigns.v1.GetMyCampaignsNameRequest
 */
export type GetMyCampaignsNameRequest =
  Message<"api.campaigns.v1.GetMyCampaignsNameRequest"> & {};

/**
 * Describes the message api.campaigns.v1.GetMyCampaignsNameRequest.
 * Use `create(GetMyCampaignsNameRequestSchema)` to create a new message.
 */
export const GetMyCampaignsNameRequestSchema: GenMessage<GetMyCampaignsNameRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 3);

/**
 * @generated from message api.campaigns.v1.CampaignNames
 */
export type CampaignNames = Message<"api.campaigns.v1.CampaignNames"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: int64 id = 2;
   */
  id: bigint;
};

/**
 * Describes the message api.campaigns.v1.CampaignNames.
 * Use `create(CampaignNamesSchema)` to create a new message.
 */
export const CampaignNamesSchema: GenMessage<CampaignNames> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 4);

/**
 * @generated from message api.campaigns.v1.GetMyCampaignNamesResponse
 */
export type GetMyCampaignNamesResponse =
  Message<"api.campaigns.v1.GetMyCampaignNamesResponse"> & {
    /**
     * @generated from field: repeated api.campaigns.v1.CampaignNames data = 1;
     */
    data: CampaignNames[];
  };

/**
 * Describes the message api.campaigns.v1.GetMyCampaignNamesResponse.
 * Use `create(GetMyCampaignNamesResponseSchema)` to create a new message.
 */
export const GetMyCampaignNamesResponseSchema: GenMessage<GetMyCampaignNamesResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 5);

/**
 * @generated from message api.campaigns.v1.AddCampaignRequest
 */
export type AddCampaignRequest =
  Message<"api.campaigns.v1.AddCampaignRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string name = 1;
     */
    name: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string description = 2;
     */
    description: string;

    /**
     * @generated from field: google.protobuf.Timestamp start_date = 3;
     */
    startDate?: Timestamp;

    /**
     * @generated from field: google.protobuf.Timestamp end_date = 4;
     */
    endDate?: Timestamp;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int32 total_budget = 5;
     */
    totalBudget: number;

    /**
     * @generated from field: repeated int64 categories = 6;
     */
    categories: bigint[];

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string thumbnail = 7;
     */
    thumbnail: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string short_description = 10;
     */
    shortDescription: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string promotional_message = 11;
     */
    promotionalMessage: string;

    /**
     * @generated from field: api.shared.v1.SocialMediaLinks social_media_links = 12;
     */
    socialMediaLinks?: SocialMediaLinks;
  };

/**
 * Describes the message api.campaigns.v1.AddCampaignRequest.
 * Use `create(AddCampaignRequestSchema)` to create a new message.
 */
export const AddCampaignRequestSchema: GenMessage<AddCampaignRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 6);

/**
 * @generated from message api.campaigns.v1.AddCampaignResponse
 */
export type AddCampaignResponse =
  Message<"api.campaigns.v1.AddCampaignResponse"> & {
    /**
     * @generated from field: api.campaigns.v1.Campaign data = 1;
     */
    data?: Campaign;
  };

/**
 * Describes the message api.campaigns.v1.AddCampaignResponse.
 * Use `create(AddCampaignResponseSchema)` to create a new message.
 */
export const AddCampaignResponseSchema: GenMessage<AddCampaignResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 7);

/**
 * @generated from message api.campaigns.v1.Campaign
 */
export type Campaign = Message<"api.campaigns.v1.Campaign"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string short_description = 3;
   */
  shortDescription: string;

  /**
   * @generated from field: google.protobuf.Timestamp start_date = 4;
   */
  startDate?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp end_date = 5;
   */
  endDate?: Timestamp;

  /**
   * @generated from field: int32 total_budget = 6;
   */
  totalBudget: number;

  /**
   * @generated from field: repeated int64 categories = 7;
   */
  categories: bigint[];

  /**
   * @generated from field: int64 vtuber_id = 8;
   */
  vtuberId: bigint;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 9;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: string thumbnail = 10;
   */
  thumbnail: string;

  /**
   * @generated from field: string promotional_message = 11;
   */
  promotionalMessage: string;

  /**
   * @generated from field: int32 total_raised = 12;
   */
  totalRaised: number;

  /**
   * @generated from field: api.shared.v1.SocialMediaLinks social_media_links = 13;
   */
  socialMediaLinks?: SocialMediaLinks;

  /**
   * @generated from field: string slug = 14;
   */
  slug: string;

  /**
   * @generated from field: optional string created_by = 15;
   */
  createdBy?: string;
};

/**
 * Describes the message api.campaigns.v1.Campaign.
 * Use `create(CampaignSchema)` to create a new message.
 */
export const CampaignSchema: GenMessage<Campaign> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 8);

/**
 * @generated from message api.campaigns.v1.RelatedCampaignRequest
 */
export type RelatedCampaignRequest =
  Message<"api.campaigns.v1.RelatedCampaignRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: repeated int64 category_id = 1;
     */
    categoryId: bigint[];
  };

/**
 * Describes the message api.campaigns.v1.RelatedCampaignRequest.
 * Use `create(RelatedCampaignRequestSchema)` to create a new message.
 */
export const RelatedCampaignRequestSchema: GenMessage<RelatedCampaignRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 9);

/**
 * @generated from message api.campaigns.v1.RelatedCampaignResponse
 */
export type RelatedCampaignResponse =
  Message<"api.campaigns.v1.RelatedCampaignResponse"> & {
    /**
     * @generated from field: repeated api.campaigns.v1.Campaign data = 1;
     */
    data: Campaign[];
  };

/**
 * Describes the message api.campaigns.v1.RelatedCampaignResponse.
 * Use `create(RelatedCampaignResponseSchema)` to create a new message.
 */
export const RelatedCampaignResponseSchema: GenMessage<RelatedCampaignResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 10);

/**
 * @generated from message api.campaigns.v1.GetAllCampaignsRequest
 */
export type GetAllCampaignsRequest =
  Message<"api.campaigns.v1.GetAllCampaignsRequest"> & {
    /**
     * @generated from field: optional int64 category_id = 1;
     */
    categoryId?: bigint;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.campaigns.v1.GetAllCampaignsRequest.
 * Use `create(GetAllCampaignsRequestSchema)` to create a new message.
 */
export const GetAllCampaignsRequestSchema: GenMessage<GetAllCampaignsRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 11);

/**
 * @generated from message api.campaigns.v1.GetAllCampaignsByVtuberRequest
 */
export type GetAllCampaignsByVtuberRequest =
  Message<"api.campaigns.v1.GetAllCampaignsByVtuberRequest"> & {
    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;

    /**
     * @generated from field: int64 vtuber_id = 2;
     */
    vtuberId: bigint;
  };

/**
 * Describes the message api.campaigns.v1.GetAllCampaignsByVtuberRequest.
 * Use `create(GetAllCampaignsByVtuberRequestSchema)` to create a new message.
 */
export const GetAllCampaignsByVtuberRequestSchema: GenMessage<GetAllCampaignsByVtuberRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 12);

/**
 * @generated from message api.campaigns.v1.GetMyCampaignsRequest
 */
export type GetMyCampaignsRequest =
  Message<"api.campaigns.v1.GetMyCampaignsRequest"> & {
    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.campaigns.v1.GetMyCampaignsRequest.
 * Use `create(GetMyCampaignsRequestSchema)` to create a new message.
 */
export const GetMyCampaignsRequestSchema: GenMessage<GetMyCampaignsRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 13);

/**
 * @generated from message api.campaigns.v1.GetAllCampaignsResponse
 */
export type GetAllCampaignsResponse =
  Message<"api.campaigns.v1.GetAllCampaignsResponse"> & {
    /**
     * @generated from field: repeated api.campaigns.v1.Campaign data = 1;
     */
    data: Campaign[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.campaigns.v1.GetAllCampaignsResponse.
 * Use `create(GetAllCampaignsResponseSchema)` to create a new message.
 */
export const GetAllCampaignsResponseSchema: GenMessage<GetAllCampaignsResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 14);

/**
 * @generated from message api.campaigns.v1.GetCampaignByIdRequest
 */
export type GetCampaignByIdRequest =
  Message<"api.campaigns.v1.GetCampaignByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string id = 1;
     */
    id: string;
  };

/**
 * Describes the message api.campaigns.v1.GetCampaignByIdRequest.
 * Use `create(GetCampaignByIdRequestSchema)` to create a new message.
 */
export const GetCampaignByIdRequestSchema: GenMessage<GetCampaignByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 15);

/**
 * @generated from message api.campaigns.v1.GetCampaignById
 */
export type GetCampaignById = Message<"api.campaigns.v1.GetCampaignById"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: string short_description = 4;
   */
  shortDescription: string;

  /**
   * @generated from field: google.protobuf.Timestamp start_date = 5;
   */
  startDate?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp end_date = 6;
   */
  endDate?: Timestamp;

  /**
   * @generated from field: int32 total_budget = 7;
   */
  totalBudget: number;

  /**
   * @generated from field: repeated int64 categories = 8;
   */
  categories: bigint[];

  /**
   * @generated from field: api.vtubers.v1.VtuberProfile vtuber = 9;
   */
  vtuber?: VtuberProfile;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 10;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: string thumbnail = 11;
   */
  thumbnail: string;

  /**
   * @generated from field: repeated api.campaigns.v1.CampaignVariantById variants = 12;
   */
  variants: CampaignVariantById[];

  /**
   * @generated from field: repeated api.campaigns.v1.CampaignBanner banners = 13;
   */
  banners: CampaignBanner[];

  /**
   * @generated from field: bool has_liked = 14;
   */
  hasLiked: boolean;

  /**
   * @generated from field: int64 like_count = 15;
   */
  likeCount: bigint;

  /**
   * @generated from field: string promotional_message = 16;
   */
  promotionalMessage: string;

  /**
   * @generated from field: int32 total_raised = 17;
   */
  totalRaised: number;

  /**
   * @generated from field: api.shared.v1.SocialMediaLinks social_media_links = 18;
   */
  socialMediaLinks?: SocialMediaLinks;

  /**
   * @generated from field: string slug = 19;
   */
  slug: string;
};

/**
 * Describes the message api.campaigns.v1.GetCampaignById.
 * Use `create(GetCampaignByIdSchema)` to create a new message.
 */
export const GetCampaignByIdSchema: GenMessage<GetCampaignById> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 16);

/**
 * @generated from message api.campaigns.v1.CampaignVariantById
 */
export type CampaignVariantById =
  Message<"api.campaigns.v1.CampaignVariantById"> & {
    /**
     * @generated from field: int64 id = 1;
     */
    id: bigint;

    /**
     * @generated from field: int64 campaign_id = 2;
     */
    campaignId: bigint;

    /**
     * @generated from field: int32 price = 3;
     */
    price: number;

    /**
     * @generated from field: string description = 4;
     */
    description: string;

    /**
     * @generated from field: int32 max_sub = 5;
     */
    maxSub: number;

    /**
     * @generated from field: string image = 6;
     */
    image: string;

    /**
     * @generated from field: google.protobuf.Timestamp created_at = 7;
     */
    createdAt?: Timestamp;

    /**
     * @generated from field: int64 sub_count = 8;
     */
    subCount: bigint;

    /**
     * @generated from field: string title = 9;
     */
    title: string;

    /**
     * @generated from field: bool has_subscribed = 10;
     */
    hasSubscribed: boolean;

    /**
     * @generated from field: int32 display_order = 11;
     */
    displayOrder: number;
  };

/**
 * Describes the message api.campaigns.v1.CampaignVariantById.
 * Use `create(CampaignVariantByIdSchema)` to create a new message.
 */
export const CampaignVariantByIdSchema: GenMessage<CampaignVariantById> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 17);

/**
 * @generated from message api.campaigns.v1.GetCampaignByIdResponse
 */
export type GetCampaignByIdResponse =
  Message<"api.campaigns.v1.GetCampaignByIdResponse"> & {
    /**
     * @generated from field: api.campaigns.v1.GetCampaignById data = 1;
     */
    data?: GetCampaignById;
  };

/**
 * Describes the message api.campaigns.v1.GetCampaignByIdResponse.
 * Use `create(GetCampaignByIdResponseSchema)` to create a new message.
 */
export const GetCampaignByIdResponseSchema: GenMessage<GetCampaignByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 18);

/**
 * @generated from message api.campaigns.v1.DeleteCampaignByIdRequest
 */
export type DeleteCampaignByIdRequest =
  Message<"api.campaigns.v1.DeleteCampaignByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message api.campaigns.v1.DeleteCampaignByIdRequest.
 * Use `create(DeleteCampaignByIdRequestSchema)` to create a new message.
 */
export const DeleteCampaignByIdRequestSchema: GenMessage<DeleteCampaignByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 19);

/**
 * @generated from message api.campaigns.v1.UpdateCampaignByIdRequest
 */
export type UpdateCampaignByIdRequest =
  Message<"api.campaigns.v1.UpdateCampaignByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string name = 1;
     */
    name: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string description = 2;
     */
    description: string;

    /**
     * @generated from field: google.protobuf.Timestamp start_date = 3;
     */
    startDate?: Timestamp;

    /**
     * @generated from field: google.protobuf.Timestamp end_date = 4;
     */
    endDate?: Timestamp;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 5;
     */
    id: bigint;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int32 total_budget = 6;
     */
    totalBudget: number;

    /**
     * @generated from field: repeated int64 categories = 7;
     */
    categories: bigint[];

    /**
     * @generated from field: string thumbnail = 8;
     */
    thumbnail: string;

    /**
     * @gotag: validate:"required";
     *
     * @generated from field: string short_description = 9;
     */
    shortDescription: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string promotional_message = 10;
     */
    promotionalMessage: string;

    /**
     * @generated from field: api.shared.v1.SocialMediaLinks social_media_links = 11;
     */
    socialMediaLinks?: SocialMediaLinks;
  };

/**
 * Describes the message api.campaigns.v1.UpdateCampaignByIdRequest.
 * Use `create(UpdateCampaignByIdRequestSchema)` to create a new message.
 */
export const UpdateCampaignByIdRequestSchema: GenMessage<UpdateCampaignByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 20);

/**
 * @generated from message api.campaigns.v1.CampaignSubscriptionComments
 */
export type CampaignSubscriptionComments =
  Message<"api.campaigns.v1.CampaignSubscriptionComments"> & {
    /**
     * @generated from field: int64 id = 1;
     */
    id: bigint;

    /**
     * @generated from field: api.shared.v1.Profile user = 2;
     */
    user?: Profile;

    /**
     * @generated from field: google.protobuf.Timestamp created_at = 3;
     */
    createdAt?: Timestamp;

    /**
     * @generated from field: string comment = 4;
     */
    comment: string;
  };

/**
 * Describes the message api.campaigns.v1.CampaignSubscriptionComments.
 * Use `create(CampaignSubscriptionCommentsSchema)` to create a new message.
 */
export const CampaignSubscriptionCommentsSchema: GenMessage<CampaignSubscriptionComments> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 21);

/**
 * @generated from message api.campaigns.v1.GetCampaignSubscriptionCommentsRequest
 */
export type GetCampaignSubscriptionCommentsRequest =
  Message<"api.campaigns.v1.GetCampaignSubscriptionCommentsRequest"> & {
    /**
     * @generated from field: int64 campaign_id = 1;
     */
    campaignId: bigint;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.campaigns.v1.GetCampaignSubscriptionCommentsRequest.
 * Use `create(GetCampaignSubscriptionCommentsRequestSchema)` to create a new message.
 */
export const GetCampaignSubscriptionCommentsRequestSchema: GenMessage<GetCampaignSubscriptionCommentsRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 22);

/**
 * @generated from message api.campaigns.v1.GetCampaignSubscriptionCommentsReponse
 */
export type GetCampaignSubscriptionCommentsReponse =
  Message<"api.campaigns.v1.GetCampaignSubscriptionCommentsReponse"> & {
    /**
     * @generated from field: repeated api.campaigns.v1.CampaignSubscriptionComments data = 1;
     */
    data: CampaignSubscriptionComments[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.campaigns.v1.GetCampaignSubscriptionCommentsReponse.
 * Use `create(GetCampaignSubscriptionCommentsReponseSchema)` to create a new message.
 */
export const GetCampaignSubscriptionCommentsReponseSchema: GenMessage<GetCampaignSubscriptionCommentsReponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 23);

/**
 * @generated from message api.campaigns.v1.GetSupportedCampaignRequest
 */
export type GetSupportedCampaignRequest =
  Message<"api.campaigns.v1.GetSupportedCampaignRequest"> & {
    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.campaigns.v1.GetSupportedCampaignRequest.
 * Use `create(GetSupportedCampaignRequestSchema)` to create a new message.
 */
export const GetSupportedCampaignRequestSchema: GenMessage<GetSupportedCampaignRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 24);

/**
 * @generated from message api.campaigns.v1.PopularCampaignRequest
 */
export type PopularCampaignRequest =
  Message<"api.campaigns.v1.PopularCampaignRequest"> & {};

/**
 * Describes the message api.campaigns.v1.PopularCampaignRequest.
 * Use `create(PopularCampaignRequestSchema)` to create a new message.
 */
export const PopularCampaignRequestSchema: GenMessage<PopularCampaignRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 25);

/**
 * @generated from message api.campaigns.v1.DeleteCampaignByIdResponse
 */
export type DeleteCampaignByIdResponse =
  Message<"api.campaigns.v1.DeleteCampaignByIdResponse"> & {
    /**
     * @generated from field: string message = 1;
     */
    message: string;

    /**
     * @generated from field: bool success = 2;
     */
    success: boolean;
  };

/**
 * Describes the message api.campaigns.v1.DeleteCampaignByIdResponse.
 * Use `create(DeleteCampaignByIdResponseSchema)` to create a new message.
 */
export const DeleteCampaignByIdResponseSchema: GenMessage<DeleteCampaignByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 26);

/**
 * @generated from message api.campaigns.v1.UpdateCampaignByIdResponse
 */
export type UpdateCampaignByIdResponse =
  Message<"api.campaigns.v1.UpdateCampaignByIdResponse"> & {
    /**
     * @generated from field: string message = 1;
     */
    message: string;

    /**
     * @generated from field: bool success = 2;
     */
    success: boolean;
  };

/**
 * Describes the message api.campaigns.v1.UpdateCampaignByIdResponse.
 * Use `create(UpdateCampaignByIdResponseSchema)` to create a new message.
 */
export const UpdateCampaignByIdResponseSchema: GenMessage<UpdateCampaignByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaigns, 27);

/**
 * @generated from service api.campaigns.v1.CampaignService
 */
export const CampaignService: GenService<{
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.AddCampaign
   */
  addCampaign: {
    methodKind: "unary";
    input: typeof AddCampaignRequestSchema;
    output: typeof AddCampaignResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.GetSubscribers
   */
  getSubscribers: {
    methodKind: "unary";
    input: typeof SubscriberRequestSchema;
    output: typeof SubscriberResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.GetAllCampaigns
   */
  getAllCampaigns: {
    methodKind: "unary";
    input: typeof GetAllCampaignsRequestSchema;
    output: typeof GetAllCampaignsResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.GetAllCampaignsByVtuberId
   */
  getAllCampaignsByVtuberId: {
    methodKind: "unary";
    input: typeof GetAllCampaignsByVtuberRequestSchema;
    output: typeof GetAllCampaignsResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.GetMyCampaigns
   */
  getMyCampaigns: {
    methodKind: "unary";
    input: typeof GetMyCampaignsRequestSchema;
    output: typeof GetAllCampaignsResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.GetCampaignById
   */
  getCampaignById: {
    methodKind: "unary";
    input: typeof GetCampaignByIdRequestSchema;
    output: typeof GetCampaignByIdResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.DeleteCampaignById
   */
  deleteCampaignById: {
    methodKind: "unary";
    input: typeof DeleteCampaignByIdRequestSchema;
    output: typeof DeleteCampaignByIdResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.UpdateCampaignById
   */
  updateCampaignById: {
    methodKind: "unary";
    input: typeof UpdateCampaignByIdRequestSchema;
    output: typeof UpdateCampaignByIdResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.GetCampaignSubscriberComments
   */
  getCampaignSubscriberComments: {
    methodKind: "unary";
    input: typeof GetCampaignSubscriptionCommentsRequestSchema;
    output: typeof GetCampaignSubscriptionCommentsReponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.GetMySupportedCampaigns
   */
  getMySupportedCampaigns: {
    methodKind: "unary";
    input: typeof GetSupportedCampaignRequestSchema;
    output: typeof GetAllCampaignsResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.GetPopularCampaign
   */
  getPopularCampaign: {
    methodKind: "unary";
    input: typeof PopularCampaignRequestSchema;
    output: typeof CampaignSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.GetRelatedCampaign
   */
  getRelatedCampaign: {
    methodKind: "unary";
    input: typeof RelatedCampaignRequestSchema;
    output: typeof RelatedCampaignResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignService.GetMyCampaignNames
   */
  getMyCampaignNames: {
    methodKind: "unary";
    input: typeof GetMyCampaignsNameRequestSchema;
    output: typeof GetMyCampaignNamesResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_campaigns_v1_campaigns, 0);
