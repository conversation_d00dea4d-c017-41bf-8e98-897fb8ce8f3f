// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file campaigns/v1/campaginvariantsubscription.proto (package api.camapigns.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";

/**
 * Describes the file campaigns/v1/campaginvariantsubscription.proto.
 */
export const file_campaigns_v1_campaginvariantsubscription: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Ci5jYW1wYWlnbnMvdjEvY2FtcGFnaW52YXJpYW50c3Vic2NyaXB0aW9uLnByb3RvEhBhcGkuY2FtYXBpZ25zLnYxIoQBCiVBZGRDYW1wYWlnblZhcmlhbnRTdWJzY3JpcHRpb25SZXF1ZXN0EhsKE2NhbXBhaWduX3ZhcmlhbnRfaWQYASABKAMSHAoUdXNlcl9iaWxsaW5nX2luZm9faWQYAiABKAMSFAoHY29tbWVudBgDIAEoCUgAiAEBQgoKCF9jb21tZW50ImUKJkFkZENhbXBhaWduVmFyaWFudFN1YnNjcmlwdGlvblJlc3BvbnNlEjsKBGRhdGEYASABKAsyLS5hcGkuY2FtYXBpZ25zLnYxLkNhbXBhaWduVmFyaWFudFN1YnNjcmlwdGlvbiL5AQobQ2FtcGFpZ25WYXJpYW50U3Vic2NyaXB0aW9uEg8KB3VzZXJfaWQYASABKAMSGwoTY2FtcGFpZ25fdmFyaWFudF9pZBgCIAEoAxIsCgdwcm9maWxlGAMgASgLMhYuYXBpLnNoYXJlZC52MS5Qcm9maWxlSACIAQESDQoFcHJpY2UYBCABKAUSLgoKY3JlYXRlZF9hdBgFIAEoCzIaLmdvb2dsZS5wcm90b2J1Zi5UaW1lc3RhbXASEQoJdnR1YmVyX2lkGAYgASgDEhQKB2NvbW1lbnQYByABKAlIAYgBAUIKCghfcHJvZmlsZUIKCghfY29tbWVudDLCAQoiQ2FtcGFpZ25WYXJpYW50U3Vic2NyaXB0aW9uU2VydmljZRKbAQoeQWRkQ2FtcGFpZ25WYXJpYW50U3Vic2NyaXB0aW9uEjcuYXBpLmNhbWFwaWducy52MS5BZGRDYW1wYWlnblZhcmlhbnRTdWJzY3JpcHRpb25SZXF1ZXN0GjguYXBpLmNhbWFwaWducy52MS5BZGRDYW1wYWlnblZhcmlhbnRTdWJzY3JpcHRpb25SZXNwb25zZSIGgrUYAggBQjhaNmdpdGh1Yi5jb20vbnNwLWluYy92dHViZXIvYXBpL2NhbXBhaWducy92MTtjYW1wYWlnbnN2MWIGcHJvdG8z",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_profile,
    ],
  );

/**
 * @generated from message api.camapigns.v1.AddCampaignVariantSubscriptionRequest
 */
export type AddCampaignVariantSubscriptionRequest =
  Message<"api.camapigns.v1.AddCampaignVariantSubscriptionRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 campaign_variant_id = 1;
     */
    campaignVariantId: bigint;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 user_billing_info_id = 2;
     */
    userBillingInfoId: bigint;

    /**
     * @generated from field: optional string comment = 3;
     */
    comment?: string;
  };

/**
 * Describes the message api.camapigns.v1.AddCampaignVariantSubscriptionRequest.
 * Use `create(AddCampaignVariantSubscriptionRequestSchema)` to create a new message.
 */
export const AddCampaignVariantSubscriptionRequestSchema: GenMessage<AddCampaignVariantSubscriptionRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaginvariantsubscription, 0);

/**
 * @generated from message api.camapigns.v1.AddCampaignVariantSubscriptionResponse
 */
export type AddCampaignVariantSubscriptionResponse =
  Message<"api.camapigns.v1.AddCampaignVariantSubscriptionResponse"> & {
    /**
     * @generated from field: api.camapigns.v1.CampaignVariantSubscription data = 1;
     */
    data?: CampaignVariantSubscription;
  };

/**
 * Describes the message api.camapigns.v1.AddCampaignVariantSubscriptionResponse.
 * Use `create(AddCampaignVariantSubscriptionResponseSchema)` to create a new message.
 */
export const AddCampaignVariantSubscriptionResponseSchema: GenMessage<AddCampaignVariantSubscriptionResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaginvariantsubscription, 1);

/**
 * @generated from message api.camapigns.v1.CampaignVariantSubscription
 */
export type CampaignVariantSubscription =
  Message<"api.camapigns.v1.CampaignVariantSubscription"> & {
    /**
     * @generated from field: int64 user_id = 1;
     */
    userId: bigint;

    /**
     * @generated from field: int64 campaign_variant_id = 2;
     */
    campaignVariantId: bigint;

    /**
     * @generated from field: optional api.shared.v1.Profile profile = 3;
     */
    profile?: Profile;

    /**
     * @generated from field: int32 price = 4;
     */
    price: number;

    /**
     * @generated from field: google.protobuf.Timestamp created_at = 5;
     */
    createdAt?: Timestamp;

    /**
     * @generated from field: int64 vtuber_id = 6;
     */
    vtuberId: bigint;

    /**
     * @generated from field: optional string comment = 7;
     */
    comment?: string;
  };

/**
 * Describes the message api.camapigns.v1.CampaignVariantSubscription.
 * Use `create(CampaignVariantSubscriptionSchema)` to create a new message.
 */
export const CampaignVariantSubscriptionSchema: GenMessage<CampaignVariantSubscription> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaginvariantsubscription, 2);

/**
 * @generated from service api.camapigns.v1.CampaignVariantSubscriptionService
 */
export const CampaignVariantSubscriptionService: GenService<{
  /**
   * @generated from rpc api.camapigns.v1.CampaignVariantSubscriptionService.AddCampaignVariantSubscription
   */
  addCampaignVariantSubscription: {
    methodKind: "unary";
    input: typeof AddCampaignVariantSubscriptionRequestSchema;
    output: typeof AddCampaignVariantSubscriptionResponseSchema;
  };
}> =
  /*@__PURE__*/
  serviceDesc(file_campaigns_v1_campaginvariantsubscription, 0);
