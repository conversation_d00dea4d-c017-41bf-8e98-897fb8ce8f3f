// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file shared/v1/pagination.proto (package api.shared.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file shared/v1/pagination.proto.
 */
export const file_shared_v1_pagination: GenFile =
  /*@__PURE__*/
  fileDesc(
    "ChpzaGFyZWQvdjEvcGFnaW5hdGlvbi5wcm90bxINYXBpLnNoYXJlZC52MSKFAQoRUGFnaW5hdGlvblJlcXVlc3QSEQoEc2l6ZRgBIAEoBUgAiAEBEhEKBHBhZ2UYAiABKAVIAYgBARIRCgRzb3J0GAMgASgJSAKIAQESEgoFb3JkZXIYBCABKAlIA4gBAUIHCgVfc2l6ZUIHCgVfcGFnZUIHCgVfc29ydEIICgZfb3JkZXIitQEKEVBhZ2luYXRpb25EZXRhaWxzEhMKC3RvdGFsX2l0ZW1zGAEgASgFEhMKC3RvdGFsX3BhZ2VzGAIgASgFEhQKDGN1cnJlbnRfcGFnZRgDIAEoBRIRCglwYWdlX3NpemUYBCABKAUSEQoJbmV4dF9wYWdlGAUgASgFEhEKCXByZXZfcGFnZRgGIAEoBRInCgVsaW5rcxgHIAMoCzIYLmFwaS5zaGFyZWQudjEuUGFnZUxpbmtzIrkBCglQYWdlTGlua3MSEwoGaXNfZG90GAEgASgISACIAQESEQoEcGFnZRgCIAEoBUgBiAEBEhQKB2lzX25leHQYAyABKAhIAogBARIWCglpc19hY3RpdmUYBCABKAhIA4gBARIYCgtpc19wcmV2aW91cxgFIAEoCEgEiAEBQgkKB19pc19kb3RCBwoFX3BhZ2VCCgoIX2lzX25leHRCDAoKX2lzX2FjdGl2ZUIOCgxfaXNfcHJldmlvdXNCMlowZ2l0aHViLmNvbS9uc3AtaW5jL3Z0dWJlci9hcGkvc2hhcmVkL3YxO3NoYXJlZHYxYgZwcm90bzM",
  );

/**
 * @generated from message api.shared.v1.PaginationRequest
 */
export type PaginationRequest = Message<"api.shared.v1.PaginationRequest"> & {
  /**
   * @generated from field: optional int32 size = 1;
   */
  size?: number;

  /**
   * @generated from field: optional int32 page = 2;
   */
  page?: number;

  /**
   * @generated from field: optional string sort = 3;
   */
  sort?: string;

  /**
   * @generated from field: optional string order = 4;
   */
  order?: string;
};

/**
 * Describes the message api.shared.v1.PaginationRequest.
 * Use `create(PaginationRequestSchema)` to create a new message.
 */
export const PaginationRequestSchema: GenMessage<PaginationRequest> =
  /*@__PURE__*/
  messageDesc(file_shared_v1_pagination, 0);

/**
 * @generated from message api.shared.v1.PaginationDetails
 */
export type PaginationDetails = Message<"api.shared.v1.PaginationDetails"> & {
  /**
   * @generated from field: int32 total_items = 1;
   */
  totalItems: number;

  /**
   * @generated from field: int32 total_pages = 2;
   */
  totalPages: number;

  /**
   * @generated from field: int32 current_page = 3;
   */
  currentPage: number;

  /**
   * @generated from field: int32 page_size = 4;
   */
  pageSize: number;

  /**
   * @generated from field: int32 next_page = 5;
   */
  nextPage: number;

  /**
   * @generated from field: int32 prev_page = 6;
   */
  prevPage: number;

  /**
   * @generated from field: repeated api.shared.v1.PageLinks links = 7;
   */
  links: PageLinks[];
};

/**
 * Describes the message api.shared.v1.PaginationDetails.
 * Use `create(PaginationDetailsSchema)` to create a new message.
 */
export const PaginationDetailsSchema: GenMessage<PaginationDetails> =
  /*@__PURE__*/
  messageDesc(file_shared_v1_pagination, 1);

/**
 * @generated from message api.shared.v1.PageLinks
 */
export type PageLinks = Message<"api.shared.v1.PageLinks"> & {
  /**
   * @generated from field: optional bool is_dot = 1;
   */
  isDot?: boolean;

  /**
   * @generated from field: optional int32 page = 2;
   */
  page?: number;

  /**
   * @generated from field: optional bool is_next = 3;
   */
  isNext?: boolean;

  /**
   * @generated from field: optional bool is_active = 4;
   */
  isActive?: boolean;

  /**
   * @generated from field: optional bool is_previous = 5;
   */
  isPrevious?: boolean;
};

/**
 * Describes the message api.shared.v1.PageLinks.
 * Use `create(PageLinksSchema)` to create a new message.
 */
export const PageLinksSchema: GenMessage<PageLinks> =
  /*@__PURE__*/
  messageDesc(file_shared_v1_pagination, 2);
