// @generated by protoc-gen-es v2.4.0 with parameter "target=ts"
// @generated from file userdeliveryaddress/v1/userdeliveryaddress.proto (package api.userdeliveryaddress.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { GenericResponseSchema } from "../../shared/v1/generic_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";

/**
 * Describes the file userdeliveryaddress/v1/userdeliveryaddress.proto.
 */
export const file_userdeliveryaddress_v1_userdeliveryaddress: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
    ],
  );

/**
 * @generated from message api.userdeliveryaddress.v1.AddUserDeliveryAddressRequest
 */
export type AddUserDeliveryAddressRequest =
  Message<"api.userdeliveryaddress.v1.AddUserDeliveryAddressRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string recipient = 1;
     */
    recipient: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string phone_number = 2;
     */
    phoneNumber: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string postal_code = 3;
     */
    postalCode: string;

    /**
     * @gotag: validate:"required,oneof=Hokkaido/北海道 Aomori/青森県 Iwate/岩手県 Miyagi/宮城県 Akita/秋田県 Yamagata/山形県 Fukushima/福島県 Ibaraki/茨城県 Tochigi/栃木県 Gunma/群馬県 Saitama/埼玉県 Chiba/千葉県 Tokyo/東京都 Kanagawa/神奈川県 Niigata/新潟県 Toyama/富山県 Ishikawa/石川県 Fukui/福井県 Yamanashi/山梨県 Nagano/長野県 Gifu/岐阜県 Shizuoka/静岡県 Aichi/愛知県 Mie/三重県 Shiga/滋賀県 Kyoto/京都府 Osaka/大阪府 Hyogo/兵庫県 Nara/奈良県 Wakayama/和歌山県 Tottori/鳥取県 Shimane/島根県 Okayama/岡山県 Hiroshima/広島県 Yamaguchi/山口県 Tokushima/徳島県 Kagawa/香川県 Ehime/愛媛県 Kochi/高知県 Fukuoka/福岡県 Saga/佐賀県 Nagasaki/長崎県 Kumamoto/熊本県 Oita/大分県 Miyazaki/宮崎県 Kagoshima/鹿児島県 Okinawa/沖縄県"
     *
     * @generated from field: string prefecture = 4;
     */
    prefecture: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string city = 5;
     */
    city: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string address_line1 = 6;
     */
    addressLine1: string;

    /**
     * @generated from field: optional string address_line2 = 7;
     */
    addressLine2?: string;

    /**
     * @generated from field: optional string preferred_delivery_time = 8;
     */
    preferredDeliveryTime?: string;

    /**
     * @generated from field: optional google.protobuf.Timestamp preferred_delivery_date = 9;
     */
    preferredDeliveryDate?: Timestamp;
  };

/**
 * Describes the message api.userdeliveryaddress.v1.AddUserDeliveryAddressRequest.
 * Use `create(AddUserDeliveryAddressRequestSchema)` to create a new message.
 */
export const AddUserDeliveryAddressRequestSchema: GenMessage<AddUserDeliveryAddressRequest> =
  /*@__PURE__*/
  messageDesc(file_userdeliveryaddress_v1_userdeliveryaddress, 0);

/**
 * @generated from message api.userdeliveryaddress.v1.AddUserDeliveryAddressResponse
 */
export type AddUserDeliveryAddressResponse =
  Message<"api.userdeliveryaddress.v1.AddUserDeliveryAddressResponse"> & {
    /**
     * @generated from field: api.userdeliveryaddress.v1.UserDeliveryAddress data = 1;
     */
    data?: UserDeliveryAddress;
  };

/**
 * Describes the message api.userdeliveryaddress.v1.AddUserDeliveryAddressResponse.
 * Use `create(AddUserDeliveryAddressResponseSchema)` to create a new message.
 */
export const AddUserDeliveryAddressResponseSchema: GenMessage<AddUserDeliveryAddressResponse> =
  /*@__PURE__*/
  messageDesc(file_userdeliveryaddress_v1_userdeliveryaddress, 1);

/**
 * @generated from message api.userdeliveryaddress.v1.UserDeliveryAddress
 */
export type UserDeliveryAddress =
  Message<"api.userdeliveryaddress.v1.UserDeliveryAddress"> & {
    /**
     * @generated from field: int64 id = 1;
     */
    id: bigint;

    /**
     * @generated from field: string recipient = 2;
     */
    recipient: string;

    /**
     * @generated from field: string phone_number = 3;
     */
    phoneNumber: string;

    /**
     * @generated from field: string postal_code = 4;
     */
    postalCode: string;

    /**
     * @generated from field: string prefecture = 5;
     */
    prefecture: string;

    /**
     * @generated from field: string city = 6;
     */
    city: string;

    /**
     * @generated from field: string address_line1 = 7;
     */
    addressLine1: string;

    /**
     * @generated from field: optional string address_line2 = 8;
     */
    addressLine2?: string;

    /**
     * @generated from field: optional string preferred_delivery_time = 9;
     */
    preferredDeliveryTime?: string;

    /**
     * @generated from field: optional google.protobuf.Timestamp preferred_delivery_date = 10;
     */
    preferredDeliveryDate?: Timestamp;

    /**
     * @generated from field: google.protobuf.Timestamp created_at = 11;
     */
    createdAt?: Timestamp;
  };

/**
 * Describes the message api.userdeliveryaddress.v1.UserDeliveryAddress.
 * Use `create(UserDeliveryAddressSchema)` to create a new message.
 */
export const UserDeliveryAddressSchema: GenMessage<UserDeliveryAddress> =
  /*@__PURE__*/
  messageDesc(file_userdeliveryaddress_v1_userdeliveryaddress, 2);

/**
 * @generated from message api.userdeliveryaddress.v1.GetCurrentUsersDeliveryAddressRequest
 */
export type GetCurrentUsersDeliveryAddressRequest =
  Message<"api.userdeliveryaddress.v1.GetCurrentUsersDeliveryAddressRequest"> & {};

/**
 * Describes the message api.userdeliveryaddress.v1.GetCurrentUsersDeliveryAddressRequest.
 * Use `create(GetCurrentUsersDeliveryAddressRequestSchema)` to create a new message.
 */
export const GetCurrentUsersDeliveryAddressRequestSchema: GenMessage<GetCurrentUsersDeliveryAddressRequest> =
  /*@__PURE__*/
  messageDesc(file_userdeliveryaddress_v1_userdeliveryaddress, 3);

/**
 * @generated from message api.userdeliveryaddress.v1.GetCurrentUsersDeliveryAddressResponse
 */
export type GetCurrentUsersDeliveryAddressResponse =
  Message<"api.userdeliveryaddress.v1.GetCurrentUsersDeliveryAddressResponse"> & {
    /**
     * @generated from field: api.userdeliveryaddress.v1.UserDeliveryAddress data = 1;
     */
    data?: UserDeliveryAddress;
  };

/**
 * Describes the message api.userdeliveryaddress.v1.GetCurrentUsersDeliveryAddressResponse.
 * Use `create(GetCurrentUsersDeliveryAddressResponseSchema)` to create a new message.
 */
export const GetCurrentUsersDeliveryAddressResponseSchema: GenMessage<GetCurrentUsersDeliveryAddressResponse> =
  /*@__PURE__*/
  messageDesc(file_userdeliveryaddress_v1_userdeliveryaddress, 4);

/**
 * @generated from message api.userdeliveryaddress.v1.GetUserDeliveryAddressRequest
 */
export type GetUserDeliveryAddressRequest =
  Message<"api.userdeliveryaddress.v1.GetUserDeliveryAddressRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message api.userdeliveryaddress.v1.GetUserDeliveryAddressRequest.
 * Use `create(GetUserDeliveryAddressRequestSchema)` to create a new message.
 */
export const GetUserDeliveryAddressRequestSchema: GenMessage<GetUserDeliveryAddressRequest> =
  /*@__PURE__*/
  messageDesc(file_userdeliveryaddress_v1_userdeliveryaddress, 5);

/**
 * @generated from message api.userdeliveryaddress.v1.GetUserDeliveryAddressResponse
 */
export type GetUserDeliveryAddressResponse =
  Message<"api.userdeliveryaddress.v1.GetUserDeliveryAddressResponse"> & {
    /**
     * @generated from field: api.userdeliveryaddress.v1.UserDeliveryAddress data = 1;
     */
    data?: UserDeliveryAddress;
  };

/**
 * Describes the message api.userdeliveryaddress.v1.GetUserDeliveryAddressResponse.
 * Use `create(GetUserDeliveryAddressResponseSchema)` to create a new message.
 */
export const GetUserDeliveryAddressResponseSchema: GenMessage<GetUserDeliveryAddressResponse> =
  /*@__PURE__*/
  messageDesc(file_userdeliveryaddress_v1_userdeliveryaddress, 6);

/**
 * @generated from message api.userdeliveryaddress.v1.DeleteUserDeliveryAddressRequest
 */
export type DeleteUserDeliveryAddressRequest =
  Message<"api.userdeliveryaddress.v1.DeleteUserDeliveryAddressRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message api.userdeliveryaddress.v1.DeleteUserDeliveryAddressRequest.
 * Use `create(DeleteUserDeliveryAddressRequestSchema)` to create a new message.
 */
export const DeleteUserDeliveryAddressRequestSchema: GenMessage<DeleteUserDeliveryAddressRequest> =
  /*@__PURE__*/
  messageDesc(file_userdeliveryaddress_v1_userdeliveryaddress, 7);

/**
 * @generated from message api.userdeliveryaddress.v1.UpdateUserDeliveryAddressRequest
 */
export type UpdateUserDeliveryAddressRequest =
  Message<"api.userdeliveryaddress.v1.UpdateUserDeliveryAddressRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string recipient = 2;
     */
    recipient: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string phone_number = 3;
     */
    phoneNumber: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string postal_code = 4;
     */
    postalCode: string;

    /**
     * @gotag: validate:"required,oneof=Hokkaido/北海道 Aomori/青森県 Iwate/岩手県 Miyagi/宮城県 Akita/秋田県 Yamagata/山形県 Fukushima/福島県 Ibaraki/茨城県 Tochigi/栃木県 Gunma/群馬県 Saitama/埼玉県 Chiba/千葉県 Tokyo/東京都 Kanagawa/神奈川県 Niigata/新潟県 Toyama/富山県 Ishikawa/石川県 Fukui/福井県 Yamanashi/山梨県 Nagano/長野県 Gifu/岐阜県 Shizuoka/静岡県 Aichi/愛知県 Mie/三重県 Shiga/滋賀県 Kyoto/京都府 Osaka/大阪府 Hyogo/兵庫県 Nara/奈良県 Wakayama/和歌山県 Tottori/鳥取県 Shimane/島根県 Okayama/岡山県 Hiroshima/広島県 Yamaguchi/山口県 Tokushima/徳島県 Kagawa/香川県 Ehime/愛媛県 Kochi/高知県 Fukuoka/福岡県 Saga/佐賀県 Nagasaki/長崎県 Kumamoto/熊本県 Oita/大分県 Miyazaki/宮崎県 Kagoshima/鹿児島県 Okinawa/沖縄県"
     *
     * @generated from field: string prefecture = 5;
     */
    prefecture: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string city = 6;
     */
    city: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string address_line1 = 7;
     */
    addressLine1: string;

    /**
     * @generated from field: optional string address_line2 = 8;
     */
    addressLine2?: string;

    /**
     * @gotag: validate:"oneof=2PM~4PM（午後2時～4時） 4PM~6PM（午後4時～6時）6PM~8PM（午後6時～8時）7PM~9PM（午後7時～
     *
     * @generated from field: optional string preferred_delivery_time = 9;
     */
    preferredDeliveryTime?: string;

    /**
     * @generated from field: optional google.protobuf.Timestamp preferred_delivery_date = 10;
     */
    preferredDeliveryDate?: Timestamp;
  };

/**
 * Describes the message api.userdeliveryaddress.v1.UpdateUserDeliveryAddressRequest.
 * Use `create(UpdateUserDeliveryAddressRequestSchema)` to create a new message.
 */
export const UpdateUserDeliveryAddressRequestSchema: GenMessage<UpdateUserDeliveryAddressRequest> =
  /*@__PURE__*/
  messageDesc(file_userdeliveryaddress_v1_userdeliveryaddress, 8);

/**
 * @generated from service api.userdeliveryaddress.v1.UserDeliveryAddressService
 */
export const UserDeliveryAddressService: GenService<{
  /**
   * @generated from rpc api.userdeliveryaddress.v1.UserDeliveryAddressService.AddUserDeliveryAddress
   */
  addUserDeliveryAddress: {
    methodKind: "unary";
    input: typeof AddUserDeliveryAddressRequestSchema;
    output: typeof AddUserDeliveryAddressResponseSchema;
  };
  /**
   * @generated from rpc api.userdeliveryaddress.v1.UserDeliveryAddressService.GetCurrentUserDeliveryAddress
   */
  getCurrentUserDeliveryAddress: {
    methodKind: "unary";
    input: typeof GetCurrentUsersDeliveryAddressRequestSchema;
    output: typeof GetCurrentUsersDeliveryAddressResponseSchema;
  };
  /**
   * @generated from rpc api.userdeliveryaddress.v1.UserDeliveryAddressService.GetUserDeliveryAddressById
   */
  getUserDeliveryAddressById: {
    methodKind: "unary";
    input: typeof GetUserDeliveryAddressRequestSchema;
    output: typeof GetUserDeliveryAddressResponseSchema;
  };
  /**
   * @generated from rpc api.userdeliveryaddress.v1.UserDeliveryAddressService.DeleteUserDeliveryAddress
   */
  deleteUserDeliveryAddress: {
    methodKind: "unary";
    input: typeof DeleteUserDeliveryAddressRequestSchema;
    output: typeof GenericResponseSchema;
  };
  /**
   * @generated from rpc api.userdeliveryaddress.v1.UserDeliveryAddressService.UpdateUserDeliveryAddress
   */
  updateUserDeliveryAddress: {
    methodKind: "unary";
    input: typeof UpdateUserDeliveryAddressRequestSchema;
    output: typeof GenericResponseSchema;
  };
}> =
  /*@__PURE__*/
  serviceDesc(file_userdeliveryaddress_v1_userdeliveryaddress, 0);
