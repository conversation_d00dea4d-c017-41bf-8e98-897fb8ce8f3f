import { useQuery } from "@tanstack/react-query";
import { Link, useLocation } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { eventVoteServiceClient } from "@vtuber/services/client";
import { AlertDialogTriggerProps } from "@vtuber/ui/components/alert-dialog";
import { buttonVariants } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { Spinner } from "@vtuber/ui/components/spinner";
import { TransitionPanel } from "@vtuber/ui/components/transition-panel";
import { cn } from "@vtuber/ui/lib/utils";
import useMeasure from "react-use-measure";
import { userPointsQueryOptions } from "~/utils/api";
import { EventVotingFinalStep } from "./event-voting/event-voting-final-step";
import { useEventVotingModal } from "./event-voting/event-voting-modal-provider";
import { EventVotingStep1 } from "./event-voting/event-voting-modal-step1";
import { EventVotingStep2 } from "./event-voting/event-voting-modal-step2";
import { EventVotingStep3 } from "./event-voting/event-voting-modal-step3";

interface Props extends AlertDialogTriggerProps {
  eventId: bigint;
  eventParticipationId: bigint;
}

export const EventVotingModal = ({
  eventId,
  eventParticipationId,
  ...props
}: Props) => {
  const { setStep, step, opened, setOpened, direction } = useEventVotingModal();
  const { session } = useAuth();
  const { pathname } = useLocation();
  const { getText } = useLanguage();

  const [ref, bounds] = useMeasure();
  const { data, isPending } = useQuery({
    queryKey: ["daily-voting"],
    queryFn: async () => {
      const [res] = await eventVoteServiceClient.getDailyPointAvailable({
        eventId,
      });
      return res;
    },
    enabled: opened,
  });

  const { data: points } = useQuery(userPointsQueryOptions(opened));

  const STEPS = [
    <EventVotingStep1 />,
    <EventVotingStep2 points={Math.floor(points?.point || 0)} />,
    <EventVotingStep3
      canVoteToday={data?.available}
      points={Math.floor(points?.point || 0)}
      eventParticipationId={eventParticipationId}
    />,
    <EventVotingFinalStep />,
  ];

  if (!session)
    return (
      <Link
        to="/login"
        search={{
          redirect: pathname,
        }}
        className={cn(
          buttonVariants({
            variant: "outline",
          }),
          "!rounded-full w-full !text-xs !font-medium",
        )}>
        {getText("vote")}
      </Link>
    );

  return (
    <Dialog
      open={opened}
      onOpenChange={(o) => {
        setOpened(o);
        setStep(0);
      }}>
      <DialogTrigger {...props} />
      <DialogContent
        withCloseButton={false}
        className="bg-background rounded-10 max-w-[396px] p-0 border-none overflow-hidden">
        <DialogHeader className="sr-only">
          <DialogTitle>{getText("vote")}</DialogTitle>
        </DialogHeader>
        {isPending ? (
          <div className="flex items-center justify-center h-52">
            <Spinner />
          </div>
        ) : (
          <TransitionPanel
            activeIndex={step}
            variants={{
              enter: (direction: number) => ({
                x: direction > 0 ? 364 : -364,
                opacity: 0,
                height: bounds.height > 0 ? bounds.height : "auto",
                position: "initial",
              }),
              center: {
                zIndex: 1,
                x: 0,
                opacity: 1,
                height: bounds.height > 0 ? bounds.height : "auto",
              },
              exit: (direction: number) => ({
                zIndex: 0,
                x: direction < 0 ? 364 : -364,
                opacity: 0,
                position: "absolute",
                top: 0,
                width: "100%",
              }),
            }}
            transition={{
              x: { type: "spring", stiffness: 300, damping: 30 },
              opacity: { duration: 0.2 },
            }}
            custom={direction}>
            {STEPS.map((feature, index) => (
              <div
                key={index}
                ref={ref}>
                {feature}
              </div>
            ))}
          </TransitionPanel>
        )}
      </DialogContent>
    </Dialog>
  );
};
