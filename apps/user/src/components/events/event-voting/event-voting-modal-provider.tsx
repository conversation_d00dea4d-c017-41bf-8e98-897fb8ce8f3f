import { EventParticipation } from "@vtuber/services/events";
import React, { createContext, useContext, useState } from "react";

export type VoteType = "special" | "daily";

type EventVotingContextType = {
  eventParticipant?: EventParticipation;
  step: number;
  setStep: (s: number) => void;
  onClose: () => void;
  opened: boolean;
  setOpened: (o: boolean) => void;
  voteType: VoteType | undefined;
  setVoteType: React.Dispatch<React.SetStateAction<VoteType | undefined>>;
  voteAmount: number;
  setVoteAmount: React.Dispatch<React.SetStateAction<number>>;
  handleSetActiveIndex: (newIndex: number) => void;
  direction: number;
  setDirection: (d: number) => void;
};

const EventVotingContext = createContext<EventVotingContextType>({
  step: 0,
  setStep: () => {},
  onClose: () => {},
  opened: false,
  setOpened: () => {},
  voteType: undefined,
  setVoteType: () => {},
  voteAmount: 0,
  setVoteAmount: () => {},
  handleSetActiveIndex: () => {},
  direction: 1,
  setDirection: () => {},
});

interface Props {
  data?: EventParticipation;
  children: React.ReactNode;
}

export const EventVotingModalProvider = ({ data, children }: Props) => {
  const [step, setStep] = useState(0);
  const [opened, setOpened] = useState(false);
  const [voteType, setVoteType] = useState<VoteType | undefined>();
  const [voteAmount, setVoteAmount] = useState(0);
  const [direction, setDirection] = useState(1);

  const handleSetActiveIndex = (newIndex: number) => {
    setDirection(newIndex > step ? 1 : -1);
    setStep(newIndex);
  };

  const onClose = () => {
    setOpened(false);
  };

  return (
    <EventVotingContext.Provider
      value={{
        eventParticipant: data,
        step,
        setStep,
        onClose,
        opened,
        setOpened,
        setVoteAmount,
        setVoteType,
        voteAmount,
        voteType,
        handleSetActiveIndex,
        direction,
        setDirection,
      }}>
      {children}
    </EventVotingContext.Provider>
  );
};

export const useEventVotingModal = () => {
  const context = useContext(EventVotingContext);
  if (!context) {
    throw new Error(
      "useEventModal must be used within a EventVotingModalProvider",
    );
  }
  return context;
};
