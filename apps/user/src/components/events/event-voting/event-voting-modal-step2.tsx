import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import { TicketIcon } from "@vtuber/ui/components/icons/ticket-icon";
import { Separator } from "@vtuber/ui/components/separator";
import { ChevronLeft, X } from "lucide-react";
import { useEventVotingModal } from "./event-voting-modal-provider";

export const EventVotingStep2 = ({ points }: { points: number }) => {
  const { onClose, handleSetActiveIndex } = useEventVotingModal();
  const { getText } = useLanguage();

  return (
    <div>
      <div className="flex flex-row items-center justify-between p-3 text-font border-b-4 border-b-[#302e41]">
        <button
          onClick={() => {
            handleSetActiveIndex(0);
          }}
          className="rounded-full size-10 hover:bg-white/10 flex items-center justify-center">
          <ChevronLeft className="!size-[22px]" />
        </button>
        <h6 className="font-bold text-xl">{getText("select_ticket")}</h6>
        <button
          onClick={onClose}
          className="rounded-full size-10 hover:bg-white/10 flex items-center justify-center">
          <X className="!size-[27px]" />
        </button>
      </div>
      <div className="px-8 py-6 grid gap-y-6">
        <div className="bg-gradient-5 rounded-[8px] py-[15px] px-4 grid gap-y-[25px]">
          <section className="flex flex-col items-center text-center text-font gap-y-[9px]">
            <h6 className="text-xl font-bold">所有投票券</h6>
            <div className="flex items-center justify-center gap-x-3">
              <TicketIcon />
              <p className="text-[22px] font-bold">
                {points}
                <span className="text-sm font-medium">{getText("ticket")}</span>
              </p>
            </div>
          </section>
          <section className="text-font grid gap-y-[9px]">
            <div className="flex items-center justify-between">
              <p className="text-sm font-bold">
                {getText("special_voting_ticket")}
              </p>
              <p>
                {points}
                {getText("ticket")}
              </p>
            </div>
            <Separator className="bg-font" />
            <div className="flex items-center justify-between">
              <p className="text-sm font-bold">
                {getText("daily_voting_ticket")}
              </p>
              <p className="text-[22px] font-bold">
                1
                <span className="text-sm font-medium">{getText("ticket")}</span>
              </p>
            </div>
          </section>
        </div>
        <div className="px-6 w-full">
          <Button
            size={"xl"}
            onClick={() => {
              handleSetActiveIndex(2);
            }}
            className="rounded-xs w-full"
            variant={"outline"}>
            {getText("select_ticket")}
          </Button>
        </div>
        <ul className="list-disc list-inside text-font text-xs font-medium space-y-1">
          <li>{getText("daily_voting_ticket_info")}</li>
          <li>{getText("special_voting_ticket_info")}</li>
          <li>{getText("exampale_vote_info")}</li>
        </ul>
      </div>
    </div>
  );
};
