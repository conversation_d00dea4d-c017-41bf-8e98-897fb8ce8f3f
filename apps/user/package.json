{"name": "@vtuber/user", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev --port 3000", "build": "tsc --noEmit && vite build", "start": "node .output/server/index.mjs", "ts-check": "tsc --noEmit"}, "dependencies": {"@bufbuild/protobuf": "^2.2.3", "@connectrpc/connect": "^2.0.1", "@connectrpc/connect-query": "latest", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-query": "latest", "@tanstack/react-router": "latest", "@tanstack/react-router-with-query": "^1.119.0", "@tanstack/react-start": "latest", "@vtuber/auth": "workspace:*", "@vtuber/cookie": "workspace:*", "@vtuber/language": "workspace:*", "@vtuber/services": "workspace:*", "@vtuber/ui": "workspace:*", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "lucide-react": "0.475.0", "motion": "^12.7.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-use-measure": "^2.1.7", "sonner": "^1.7.4", "vinxi": "0.5.2", "vite": "^6.3.5", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22.13.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "vite-tsconfig-paths": "^5.1.4"}}